# GraphVisualization 组件文档

## 概述

`GraphVisualization` 是 Graph Memory 系统的核心可视化组件，基于 React Flow 构建，提供了高性能的图数据可视化功能。支持多种布局算法、交互操作和性能优化。

## 组件特性

- 🎯 **多布局支持**: 力导向、层次、圆形、网格布局
- 🚀 **高性能渲染**: 支持大规模数据集的流畅渲染
- 📱 **响应式设计**: 自动适配桌面端和移动端
- 🎨 **自定义样式**: 支持主题切换和自定义节点/边样式
- 🔍 **交互功能**: 缩放、平移、选择、拖拽等完整交互
- ⚡ **性能优化**: 集成虚拟化渲染和LOD系统

## API 接口

### Props

```typescript
interface GraphVisualizationProps {
  /** 组件样式类名 */
  className?: string;
  
  /** 容器高度 */
  height?: string | number;
  
  /** 节点点击事件 */
  onNodeClick?: (node: Node) => void;
  
  /** 边点击事件 */
  onEdgeClick?: (edge: Edge) => void;
  
  /** 选择变化事件 */
  onSelectionChange?: (selectedNodes: Node[], selectedEdges: Edge[]) => void;
  
  /** 布局变化事件 */
  onLayoutChange?: (layout: LayoutType) => void;
  
  /** 视口变化事件 */
  onViewportChange?: (viewport: Viewport) => void;
}
```

### 支持的布局类型

```typescript
type LayoutType = 'force' | 'hierarchical' | 'circular' | 'grid';
```

## 使用示例

### 基础使用

```tsx
import React from 'react';
import GraphVisualization from '@/components/graph/GraphVisualization';

export default function BasicGraph() {
  return (
    <div className="w-full h-screen">
      <GraphVisualization 
        height="100vh"
        onNodeClick={(node) => {
          console.log('Clicked node:', node.data.label);
        }}
        onEdgeClick={(edge) => {
          console.log('Clicked edge:', edge.data.label);
        }}
      />
    </div>
  );
}
```

### 高级配置

```tsx
import React, { useState, useCallback } from 'react';
import GraphVisualization from '@/components/graph/GraphVisualization';

export default function AdvancedGraph() {
  const [selectedNodes, setSelectedNodes] = useState([]);
  const [currentLayout, setCurrentLayout] = useState('force');

  const handleSelectionChange = useCallback((nodes, edges) => {
    setSelectedNodes(nodes);
    console.log(`Selected ${nodes.length} nodes and ${edges.length} edges`);
  }, []);

  const handleLayoutChange = useCallback((layout) => {
    setCurrentLayout(layout);
    console.log('Layout changed to:', layout);
  }, []);

  return (
    <div className="w-full h-screen relative">
      <GraphVisualization 
        className="border border-gray-200 rounded-lg"
        height="calc(100vh - 100px)"
        onSelectionChange={handleSelectionChange}
        onLayoutChange={handleLayoutChange}
      />
      
      <div className="absolute top-4 left-4 bg-white p-2 rounded shadow">
        <p>Current Layout: {currentLayout}</p>
        <p>Selected Nodes: {selectedNodes.length}</p>
      </div>
    </div>
  );
}
```

### 与Redux集成

```tsx
import React from 'react';
import { useSelector, useDispatch } from 'react-redux';
import GraphVisualization from '@/components/graph/GraphVisualization';
import { selectNode, selectEdge } from '@/store/graphMemorySlice';

export default function ReduxIntegratedGraph() {
  const dispatch = useDispatch();
  const { nodes, edges, selectedNodeIds } = useSelector(state => state.graphMemory);

  const handleNodeClick = useCallback((node) => {
    dispatch(selectNode(node.id));
  }, [dispatch]);

  const handleEdgeClick = useCallback((edge) => {
    dispatch(selectEdge(edge.id));
  }, [dispatch]);

  return (
    <GraphVisualization 
      onNodeClick={handleNodeClick}
      onEdgeClick={handleEdgeClick}
    />
  );
}
```

## 内部架构

### 组件结构

```
GraphVisualization
├── React Flow Container
│   ├── Custom Node Types
│   │   ├── GraphNode
│   │   └── EntityNode
│   ├── Custom Edge Types
│   │   ├── GraphEdge
│   │   └── RelationEdge
│   └── Controls
│       ├── Zoom Controls
│       ├── Layout Selector
│       └── Minimap
├── Performance Layer
│   ├── VirtualizedRenderer
│   ├── LOD Manager
│   └── Performance Monitor
├── Mobile Layer
│   ├── Touch Gesture Handler
│   ├── Mobile Controls
│   └── Responsive Layout
└── Error Boundary
    ├── Error Catcher
    ├── Fallback UI
    └── Recovery Actions
```

### 性能优化机制

1. **设备性能评估**
   ```typescript
   // 自动评估设备性能并选择合适的渲染策略
   const capability = await performanceManager.assessDeviceCapability();
   if (capability.category === 'low') {
     // 启用低性能模式
     setLODLevel(LODLevel.LOW);
     setMaxNodes(50);
   }
   ```

2. **虚拟化渲染**
   ```typescript
   // 只渲染视口内的元素
   const visibleNodes = useMemo(() => {
     return nodes.filter(node => isInViewport(node.position, viewport));
   }, [nodes, viewport]);
   ```

3. **LOD (Level of Detail) 渲染**
   ```typescript
   // 根据缩放级别调整细节
   const lodLevel = useMemo(() => {
     if (zoom < 0.5) return LODLevel.MINIMAL;
     if (zoom < 1.0) return LODLevel.LOW;
     if (zoom < 2.0) return LODLevel.MEDIUM;
     return LODLevel.HIGH;
   }, [zoom]);
   ```

## 布局算法

### 1. 力导向布局 (Force Layout)

```typescript
// 使用 d3-force 算法
const forceLayout = {
  charge: -300,
  distance: 100,
  strength: 0.1,
  iterations: 300
};
```

**适用场景**: 
- 展示节点间的自然关系
- 中小规模数据集 (< 500 节点)
- 探索性数据分析

### 2. 层次布局 (Hierarchical Layout)

```typescript
// 使用 dagre 算法
const hierarchicalLayout = {
  direction: 'TB', // Top to Bottom
  nodeSpacing: 100,
  levelSpacing: 150,
  alignment: 'center'
};
```

**适用场景**:
- 有明确层级关系的数据
- 组织架构、依赖关系图
- 流程图和决策树

### 3. 圆形布局 (Circular Layout)

```typescript
// 节点按圆形排列
const circularLayout = {
  radius: 200,
  startAngle: 0,
  endAngle: 2 * Math.PI,
  clockwise: true
};
```

**适用场景**:
- 展示循环关系
- 社交网络分析
- 对称性数据展示

### 4. 网格布局 (Grid Layout)

```typescript
// 节点按网格排列
const gridLayout = {
  columns: 10,
  rowSpacing: 100,
  columnSpacing: 100,
  alignment: 'center'
};
```

**适用场景**:
- 大量节点的整齐展示
- 比较和分析场景
- 表格式数据可视化

## 移动端适配

### 自动检测和切换

```typescript
// 自动检测移动设备
const isMobile = useMemo(() => {
  return /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
}, []);

// 移动端专用配置
const mobileConfig = {
  nodesDraggable: false,
  panOnScroll: false,
  zoomOnScroll: false,
  zoomOnPinch: true,
  minZoom: 0.1,
  maxZoom: 2
};
```

### 触摸手势支持

- **单指平移**: 拖动画布
- **双指缩放**: 缩放视图
- **单击选择**: 选择节点/边
- **长按菜单**: 显示上下文菜单

## 错误处理

### 错误边界保护

```tsx
<GraphErrorBoundary
  onError={(error, errorInfo) => {
    console.error('Graph visualization error:', error);
    // 发送错误报告
  }}
  fallback={<GraphErrorFallback />}
>
  <GraphVisualization />
</GraphErrorBoundary>
```

### 常见错误处理

1. **数据格式错误**
   ```typescript
   // 数据验证
   const validateGraphData = (nodes, edges) => {
     if (!Array.isArray(nodes) || !Array.isArray(edges)) {
       throw new Error('Invalid graph data format');
     }
     // 更多验证逻辑...
   };
   ```

2. **渲染性能问题**
   ```typescript
   // 性能监控和降级
   if (renderTime > 100) {
     console.warn('Slow rendering detected, enabling performance mode');
     setPerformanceMode(true);
   }
   ```

## 自定义和扩展

### 自定义节点类型

```tsx
import { memo } from 'react';
import { Handle, Position } from 'reactflow';

const CustomNode = memo(({ data }) => {
  return (
    <div className="custom-node">
      <Handle type="target" position={Position.Top} />
      <div className="node-content">
        <h3>{data.label}</h3>
        <p>{data.description}</p>
      </div>
      <Handle type="source" position={Position.Bottom} />
    </div>
  );
});

// 注册自定义节点类型
const nodeTypes = {
  customNode: CustomNode
};
```

### 自定义边类型

```tsx
import { memo } from 'react';
import { getBezierPath } from 'reactflow';

const CustomEdge = memo(({ id, sourceX, sourceY, targetX, targetY, data }) => {
  const [edgePath] = getBezierPath({
    sourceX,
    sourceY,
    targetX,
    targetY,
  });

  return (
    <g>
      <path
        id={id}
        className="react-flow__edge-path"
        d={edgePath}
        strokeWidth={data.weight || 1}
        stroke={data.color || '#b1b1b7'}
      />
      <text>
        <textPath href={`#${id}`} startOffset="50%" textAnchor="middle">
          {data.label}
        </textPath>
      </text>
    </g>
  );
});

// 注册自定义边类型
const edgeTypes = {
  customEdge: CustomEdge
};
```

## 性能监控

### 启用性能监控

```tsx
import GraphPerformanceMonitor from '@/components/graph/GraphPerformanceMonitor';

export default function MonitoredGraph() {
  const [showMonitor, setShowMonitor] = useState(true);

  return (
    <div>
      <GraphVisualization />
      <GraphPerformanceMonitor
        isVisible={showMonitor}
        onToggle={setShowMonitor}
      />
    </div>
  );
}
```

### 性能指标

- **渲染时间**: 每帧渲染耗时
- **帧率**: 实时FPS
- **内存使用**: JS堆内存占用
- **节点/边数量**: 当前渲染的元素数量
- **设备性能**: 设备能力评分

## 最佳实践

1. **数据量控制**: 单次渲染不超过1000个节点
2. **缓存策略**: 合理使用React.memo和useMemo
3. **事件处理**: 使用useCallback避免不必要的重渲染
4. **错误边界**: 始终包装在错误边界组件中
5. **性能监控**: 在开发环境启用性能监控
6. **移动端优化**: 在移动设备上禁用复杂动画

## 故障排除

### 常见问题

1. **节点重叠**: 调整布局参数或使用不同的布局算法
2. **性能卡顿**: 启用虚拟化渲染或减少节点数量
3. **移动端交互问题**: 检查触摸事件配置
4. **内存泄漏**: 确保正确清理事件监听器和定时器

### 调试工具

- React DevTools Profiler
- 内置性能监控面板
- 浏览器开发者工具
- Redux DevTools
