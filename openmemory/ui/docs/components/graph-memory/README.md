# Graph Memory 组件文档

## 概述

Graph Memory 是 OpenMemory-UI 的核心功能模块，提供了完整的图记忆可视化和管理界面。基于 React Flow 构建，支持大规模数据的高性能渲染、智能交互和全面的管理功能。

## 架构设计

### 核心组件架构

```
Graph Memory System
├── 数据层 (Data Layer)
│   ├── Redux Store (graphMemorySlice)
│   ├── API Client (realMem0Client)
│   └── Data Transformers
├── 业务逻辑层 (Business Logic)
│   ├── useGraphMemoryApi Hook
│   ├── Performance Manager
│   └── Cache Manager
├── 展示层 (Presentation Layer)
│   ├── GraphVisualization (核心可视化)
│   ├── Management Panels (管理面板)
│   ├── Filter Components (筛选组件)
│   └── Statistics Components (统计组件)
└── 优化层 (Optimization Layer)
    ├── Error Boundaries
    ├── Performance Monitoring
    ├── Mobile Optimization
    └── Virtualization
```

### 技术栈

- **前端框架**: Next.js 15 + React 19
- **状态管理**: Redux Toolkit
- **图可视化**: React Flow 11.10.1
- **类型系统**: TypeScript
- **样式系统**: Tailwind CSS + shadcn/ui
- **性能优化**: 自研性能管理系统

## 组件清单

### 1. 核心可视化组件

| 组件名称 | 文件路径 | 功能描述 |
|---------|----------|----------|
| GraphVisualization | `components/graph/GraphVisualization.tsx` | 主要的图可视化组件 |
| GraphNode | `components/graph/GraphNode.tsx` | 自定义节点组件 |
| GraphEdge | `components/graph/GraphEdge.tsx` | 自定义边组件 |
| VirtualizedGraphRenderer | `components/graph/VirtualizedGraphRenderer.tsx` | 虚拟化渲染器 |
| MobileGraphInterface | `components/graph/MobileGraphInterface.tsx` | 移动端专用界面 |

### 2. 管理面板组件

| 组件名称 | 文件路径 | 功能描述 |
|---------|----------|----------|
| EntityPanel | `components/graph/EntityPanel.tsx` | 实体管理面板 |
| RelationshipPanel | `components/graph/RelationshipPanel.tsx` | 关系管理面板 |
| GraphHistory | `components/graph/GraphHistory.tsx` | 操作历史时间线 |

### 3. 筛选和统计组件

| 组件名称 | 文件路径 | 功能描述 |
|---------|----------|----------|
| GraphMemoryFilters | `components/graph/GraphMemoryFilters.tsx` | 高级筛选器 |
| GraphStats | `components/graph/GraphStats.tsx` | 统计信息卡片 |

### 4. 性能和监控组件

| 组件名称 | 文件路径 | 功能描述 |
|---------|----------|----------|
| GraphPerformanceMonitor | `components/graph/GraphPerformanceMonitor.tsx` | 性能监控面板 |
| GraphErrorBoundary | `components/common/GraphErrorBoundary.tsx` | 错误边界组件 |

### 5. 核心工具和Hook

| 名称 | 文件路径 | 功能描述 |
|------|----------|----------|
| useGraphMemoryApi | `hooks/useGraphMemoryApi.ts` | Graph Memory API Hook |
| GraphPerformanceManager | `lib/performance/GraphPerformanceManager.ts` | 性能管理器 |
| graph-data-transformer | `lib/graph-data-transformer.ts` | 数据转换工具 |

## 快速开始

### 1. 基础使用

```tsx
import React from 'react';
import GraphVisualization from '@/components/graph/GraphVisualization';

export default function MyGraphPage() {
  return (
    <div className="w-full h-screen">
      <GraphVisualization 
        height="100vh"
        onNodeClick={(node) => console.log('Node clicked:', node)}
        onEdgeClick={(edge) => console.log('Edge clicked:', edge)}
      />
    </div>
  );
}
```

### 2. 使用API Hook

```tsx
import { useGraphMemoryApi } from '@/hooks/useGraphMemoryApi';

export default function GraphComponent() {
  const { 
    fetchGraphMemories, 
    createEntity, 
    createRelation,
    isLoading,
    error 
  } = useGraphMemoryApi();

  const handleLoadData = async () => {
    try {
      const { nodes, edges } = await fetchGraphMemories();
      console.log('Loaded:', nodes.length, 'nodes and', edges.length, 'edges');
    } catch (err) {
      console.error('Failed to load graph data:', err);
    }
  };

  return (
    <div>
      <button onClick={handleLoadData} disabled={isLoading}>
        {isLoading ? 'Loading...' : 'Load Graph Data'}
      </button>
      {error && <div className="text-red-500">Error: {error}</div>}
    </div>
  );
}
```

### 3. 性能监控

```tsx
import GraphPerformanceMonitor from '@/components/graph/GraphPerformanceMonitor';

export default function GraphWithMonitoring() {
  const [showMonitor, setShowMonitor] = useState(false);

  return (
    <div>
      <GraphVisualization />
      <GraphPerformanceMonitor
        isVisible={showMonitor}
        onToggle={setShowMonitor}
      />
    </div>
  );
}
```

## 配置选项

### GraphVisualization 配置

```tsx
interface GraphVisualizationProps {
  className?: string;
  height?: string | number;
  onNodeClick?: (node: Node) => void;
  onEdgeClick?: (edge: Edge) => void;
  onSelectionChange?: (selectedNodes: Node[], selectedEdges: Edge[]) => void;
}
```

### 性能配置

```tsx
// 在 GraphPerformanceManager 中配置性能策略
const performanceManager = GraphPerformanceManager.getInstance();

// 获取设备性能评估
const capability = await performanceManager.assessDeviceCapability();

// 选择渲染策略
const strategy = await performanceManager.selectRenderingStrategy();
```

## 数据格式

### 节点数据格式

```typescript
interface GraphNode {
  id: string;
  type: 'graphNode';
  position: { x: number; y: number };
  data: {
    label: string;
    type: EntityType;
    description?: string;
    properties?: Record<string, any>;
    created_at: string;
    updated_at: string;
  };
}
```

### 边数据格式

```typescript
interface GraphEdge {
  id: string;
  type: 'graphEdge';
  source: string;
  target: string;
  data: {
    label: string;
    type: string;
    weight?: number;
    properties?: Record<string, any>;
    created_at: string;
    updated_at: string;
  };
}
```

## 最佳实践

### 1. 性能优化

- **大数据量处理**: 使用 VirtualizedGraphRenderer 处理超过500个节点的场景
- **移动端优化**: 在移动设备上自动切换到 MobileGraphInterface
- **缓存策略**: 合理使用 useGraphMemoryApi 的缓存功能
- **LOD渲染**: 让系统自动根据缩放级别调整细节级别

### 2. 错误处理

```tsx
import GraphErrorBoundary from '@/components/common/GraphErrorBoundary';

export default function SafeGraphComponent() {
  return (
    <GraphErrorBoundary
      onError={(error, errorInfo) => {
        // 发送错误报告到监控服务
        console.error('Graph Error:', error, errorInfo);
      }}
      showErrorDetails={process.env.NODE_ENV === 'development'}
    >
      <GraphVisualization />
    </GraphErrorBoundary>
  );
}
```

### 3. 状态管理

```tsx
import { useSelector, useDispatch } from 'react-redux';
import { updateFilters, clearSelection } from '@/store/graphMemorySlice';

export default function GraphControls() {
  const dispatch = useDispatch();
  const { filters, selectedNodeIds } = useSelector(state => state.graphMemory);

  const handleFilterChange = (newFilters) => {
    dispatch(updateFilters(newFilters));
  };

  const handleClearSelection = () => {
    dispatch(clearSelection());
  };

  return (
    <div>
      {/* 控制组件 */}
    </div>
  );
}
```

## 故障排除

### 常见问题

1. **构建错误: "window is not defined"**
   - 原因: 组件在服务端渲染时访问了浏览器API
   - 解决: 使用动态导入 `dynamic(() => import(...), { ssr: false })`

2. **性能问题: 大量节点导致页面卡顿**
   - 原因: 同时渲染过多元素
   - 解决: 启用虚拟化渲染或调整LOD设置

3. **移动端交互问题**
   - 原因: 触摸事件处理不当
   - 解决: 使用 MobileGraphInterface 组件

4. **数据加载失败**
   - 原因: API调用错误或网络问题
   - 解决: 检查 useGraphMemoryApi 的错误处理

### 调试技巧

1. **启用性能监控**
   ```tsx
   <GraphPerformanceMonitor isVisible={true} />
   ```

2. **查看Redux状态**
   ```tsx
   const graphState = useSelector(state => state.graphMemory);
   console.log('Graph State:', graphState);
   ```

3. **检查API调用**
   ```tsx
   const { error, isLoading } = useGraphMemoryApi();
   console.log('API Status:', { error, isLoading });
   ```

## 更新日志

### v2.0.0 (2025-01-29)
- ✅ 完整的Graph Memory功能实现
- ✅ 高性能虚拟化渲染
- ✅ 移动端优化
- ✅ 错误边界和监控
- ✅ 完善的文档和开发指南

### 下一步计划
- 🔄 国际化支持
- 🔄 更多图布局算法
- 🔄 高级分析功能
- 🔄 协作功能

## 贡献指南

请参考项目根目录的 CONTRIBUTING.md 文件了解如何为 Graph Memory 功能贡献代码。

## 许可证

本项目采用 MIT 许可证，详见 LICENSE 文件。
