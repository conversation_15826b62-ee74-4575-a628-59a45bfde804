# useGraphMemoryApi Hook 文档

## 概述

`useGraphMemoryApi` 是 Graph Memory 系统的核心数据管理 Hook，提供了完整的 API 调用、状态管理、缓存优化和错误处理功能。它是连接前端组件和 Mem0 Graph Memory API 的桥梁。

## 核心特性

- 🔄 **完整的CRUD操作**: 支持实体和关系的创建、读取、更新、删除
- 🚀 **智能缓存系统**: 自动缓存管理，提升性能和用户体验
- 📊 **分页和懒加载**: 支持大数据集的高效加载
- 🔍 **高级搜索**: 语义搜索、内容搜索、结构搜索
- ⚡ **批量操作**: 支持批量创建、更新、删除操作
- 🛡️ **错误处理**: 完善的错误捕获和重试机制
- 📱 **离线支持**: 基础的离线数据缓存

## API 接口

### Hook 返回值

```typescript
interface UseGraphMemoryApiReturn {
  // 数据获取
  fetchGraphMemories: (filters?: GraphMemoryFilters, options?: FetchOptions) => Promise<GraphData>;
  fetchEntityDetails: (entityId: string) => Promise<Entity>;
  fetchRelationDetails: (relationId: string) => Promise<Relation>;
  
  // 实体操作
  createEntity: (entity: CreateEntityRequest) => Promise<Entity>;
  updateEntity: (entityId: string, updates: UpdateEntityRequest) => Promise<Entity>;
  deleteEntity: (entityId: string) => Promise<void>;
  
  // 关系操作
  createRelation: (relation: CreateRelationRequest) => Promise<Relation>;
  updateRelation: (relationId: string, updates: UpdateRelationRequest) => Promise<Relation>;
  deleteRelation: (relationId: string) => Promise<void>;
  
  // 批量操作
  batchCreateEntities: (entities: CreateEntityRequest[]) => Promise<Entity[]>;
  batchDeleteEntities: (entityIds: string[]) => Promise<void>;
  batchCreateRelations: (relations: CreateRelationRequest[]) => Promise<Relation[]>;
  batchDeleteRelations: (relationIds: string[]) => Promise<void>;
  
  // 搜索功能
  searchEntities: (query: SearchQuery) => Promise<Entity[]>;
  searchRelations: (query: SearchQuery) => Promise<Relation[]>;
  semanticSearch: (query: string, options?: SemanticSearchOptions) => Promise<SearchResult[]>;
  
  // 统计信息
  getGraphStats: () => Promise<GraphStats>;
  
  // 状态管理
  isLoading: boolean;
  error: string | null;
  clearError: () => void;
  
  // 缓存管理
  clearCache: () => void;
  getCacheStats: () => CacheStats;
}
```

### 配置选项

```typescript
interface FetchOptions {
  useCache?: boolean;        // 是否使用缓存，默认 true
  loadMore?: boolean;        // 是否为加载更多，默认 false
  pageSize?: number;         // 分页大小，默认 50
  timeout?: number;          // 请求超时时间，默认 30000ms
}

interface SemanticSearchOptions {
  limit?: number;            // 结果数量限制，默认 20
  threshold?: number;        // 相似度阈值，默认 0.7
  includeRelations?: boolean; // 是否包含关系，默认 true
}
```

## 使用示例

### 基础数据获取

```tsx
import React, { useEffect, useState } from 'react';
import { useGraphMemoryApi } from '@/hooks/useGraphMemoryApi';

export default function GraphDataLoader() {
  const { fetchGraphMemories, isLoading, error } = useGraphMemoryApi();
  const [graphData, setGraphData] = useState(null);

  useEffect(() => {
    const loadData = async () => {
      try {
        const data = await fetchGraphMemories();
        setGraphData(data);
        console.log(`Loaded ${data.nodes.length} nodes and ${data.edges.length} edges`);
      } catch (err) {
        console.error('Failed to load graph data:', err);
      }
    };

    loadData();
  }, [fetchGraphMemories]);

  if (isLoading) return <div>Loading graph data...</div>;
  if (error) return <div>Error: {error}</div>;
  if (!graphData) return <div>No data available</div>;

  return (
    <div>
      <h2>Graph Data Loaded</h2>
      <p>Nodes: {graphData.nodes.length}</p>
      <p>Edges: {graphData.edges.length}</p>
    </div>
  );
}
```

### 实体管理

```tsx
import React, { useState } from 'react';
import { useGraphMemoryApi } from '@/hooks/useGraphMemoryApi';

export default function EntityManager() {
  const { 
    createEntity, 
    updateEntity, 
    deleteEntity, 
    isLoading, 
    error 
  } = useGraphMemoryApi();
  
  const [entityName, setEntityName] = useState('');
  const [entityType, setEntityType] = useState('person');

  const handleCreateEntity = async () => {
    try {
      const newEntity = await createEntity({
        name: entityName,
        type: entityType,
        properties: {
          description: `A ${entityType} named ${entityName}`
        }
      });
      console.log('Created entity:', newEntity);
      setEntityName('');
    } catch (err) {
      console.error('Failed to create entity:', err);
    }
  };

  const handleUpdateEntity = async (entityId: string, updates: any) => {
    try {
      const updatedEntity = await updateEntity(entityId, updates);
      console.log('Updated entity:', updatedEntity);
    } catch (err) {
      console.error('Failed to update entity:', err);
    }
  };

  const handleDeleteEntity = async (entityId: string) => {
    if (confirm('Are you sure you want to delete this entity?')) {
      try {
        await deleteEntity(entityId);
        console.log('Entity deleted successfully');
      } catch (err) {
        console.error('Failed to delete entity:', err);
      }
    }
  };

  return (
    <div className="space-y-4">
      <div>
        <h3>Create New Entity</h3>
        <input
          type="text"
          value={entityName}
          onChange={(e) => setEntityName(e.target.value)}
          placeholder="Entity name"
          className="border p-2 mr-2"
        />
        <select
          value={entityType}
          onChange={(e) => setEntityType(e.target.value)}
          className="border p-2 mr-2"
        >
          <option value="person">Person</option>
          <option value="organization">Organization</option>
          <option value="concept">Concept</option>
          <option value="event">Event</option>
        </select>
        <button
          onClick={handleCreateEntity}
          disabled={isLoading || !entityName}
          className="bg-blue-500 text-white p-2 rounded disabled:opacity-50"
        >
          {isLoading ? 'Creating...' : 'Create Entity'}
        </button>
      </div>
      
      {error && (
        <div className="text-red-500 p-2 border border-red-300 rounded">
          Error: {error}
        </div>
      )}
    </div>
  );
}
```

### 关系管理

```tsx
import React, { useState } from 'react';
import { useGraphMemoryApi } from '@/hooks/useGraphMemoryApi';

export default function RelationshipManager() {
  const { 
    createRelation, 
    updateRelation, 
    deleteRelation,
    isLoading 
  } = useGraphMemoryApi();
  
  const [sourceId, setSourceId] = useState('');
  const [targetId, setTargetId] = useState('');
  const [relationType, setRelationType] = useState('knows');
  const [weight, setWeight] = useState(1.0);

  const handleCreateRelation = async () => {
    try {
      const newRelation = await createRelation({
        source_id: sourceId,
        target_id: targetId,
        type: relationType,
        weight: weight,
        properties: {
          created_by: 'user',
          confidence: 0.9
        }
      });
      console.log('Created relation:', newRelation);
      // 重置表单
      setSourceId('');
      setTargetId('');
      setWeight(1.0);
    } catch (err) {
      console.error('Failed to create relation:', err);
    }
  };

  return (
    <div className="space-y-4">
      <h3>Create New Relationship</h3>
      <div className="grid grid-cols-2 gap-4">
        <input
          type="text"
          value={sourceId}
          onChange={(e) => setSourceId(e.target.value)}
          placeholder="Source Entity ID"
          className="border p-2"
        />
        <input
          type="text"
          value={targetId}
          onChange={(e) => setTargetId(e.target.value)}
          placeholder="Target Entity ID"
          className="border p-2"
        />
        <select
          value={relationType}
          onChange={(e) => setRelationType(e.target.value)}
          className="border p-2"
        >
          <option value="knows">Knows</option>
          <option value="works_with">Works With</option>
          <option value="related_to">Related To</option>
          <option value="part_of">Part Of</option>
        </select>
        <input
          type="number"
          value={weight}
          onChange={(e) => setWeight(parseFloat(e.target.value))}
          min="0"
          max="1"
          step="0.1"
          placeholder="Weight (0-1)"
          className="border p-2"
        />
      </div>
      <button
        onClick={handleCreateRelation}
        disabled={isLoading || !sourceId || !targetId}
        className="bg-green-500 text-white p-2 rounded disabled:opacity-50"
      >
        {isLoading ? 'Creating...' : 'Create Relationship'}
      </button>
    </div>
  );
}
```

### 高级搜索

```tsx
import React, { useState } from 'react';
import { useGraphMemoryApi } from '@/hooks/useGraphMemoryApi';

export default function AdvancedSearch() {
  const { searchEntities, semanticSearch, isLoading } = useGraphMemoryApi();
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState([]);
  const [searchType, setSearchType] = useState('semantic');

  const handleSearch = async () => {
    try {
      let results = [];
      
      if (searchType === 'semantic') {
        results = await semanticSearch(searchQuery, {
          limit: 20,
          threshold: 0.7,
          includeRelations: true
        });
      } else {
        results = await searchEntities({
          query: searchQuery,
          type: 'content',
          filters: {}
        });
      }
      
      setSearchResults(results);
      console.log(`Found ${results.length} results`);
    } catch (err) {
      console.error('Search failed:', err);
    }
  };

  return (
    <div className="space-y-4">
      <div>
        <h3>Advanced Search</h3>
        <div className="flex space-x-2 mb-2">
          <input
            type="text"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            placeholder="Enter search query..."
            className="flex-1 border p-2"
          />
          <select
            value={searchType}
            onChange={(e) => setSearchType(e.target.value)}
            className="border p-2"
          >
            <option value="semantic">Semantic Search</option>
            <option value="content">Content Search</option>
          </select>
          <button
            onClick={handleSearch}
            disabled={isLoading || !searchQuery}
            className="bg-purple-500 text-white p-2 rounded disabled:opacity-50"
          >
            {isLoading ? 'Searching...' : 'Search'}
          </button>
        </div>
      </div>

      <div>
        <h4>Search Results ({searchResults.length})</h4>
        <div className="space-y-2 max-h-60 overflow-y-auto">
          {searchResults.map((result, index) => (
            <div key={index} className="border p-2 rounded">
              <div className="font-semibold">{result.name || result.label}</div>
              <div className="text-sm text-gray-600">{result.type}</div>
              {result.score && (
                <div className="text-xs text-blue-600">
                  Relevance: {(result.score * 100).toFixed(1)}%
                </div>
              )}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
```

### 批量操作

```tsx
import React, { useState } from 'react';
import { useGraphMemoryApi } from '@/hooks/useGraphMemoryApi';

export default function BatchOperations() {
  const { 
    batchCreateEntities, 
    batchDeleteEntities, 
    batchCreateRelations,
    isLoading 
  } = useGraphMemoryApi();
  
  const [selectedEntityIds, setSelectedEntityIds] = useState([]);

  const handleBatchCreateEntities = async () => {
    const entities = [
      { name: 'Entity 1', type: 'person', properties: {} },
      { name: 'Entity 2', type: 'person', properties: {} },
      { name: 'Entity 3', type: 'organization', properties: {} }
    ];

    try {
      const createdEntities = await batchCreateEntities(entities);
      console.log(`Created ${createdEntities.length} entities`);
    } catch (err) {
      console.error('Batch create failed:', err);
    }
  };

  const handleBatchDelete = async () => {
    if (selectedEntityIds.length === 0) {
      alert('Please select entities to delete');
      return;
    }

    if (confirm(`Delete ${selectedEntityIds.length} entities?`)) {
      try {
        await batchDeleteEntities(selectedEntityIds);
        console.log('Batch delete completed');
        setSelectedEntityIds([]);
      } catch (err) {
        console.error('Batch delete failed:', err);
      }
    }
  };

  return (
    <div className="space-y-4">
      <h3>Batch Operations</h3>
      
      <div className="flex space-x-2">
        <button
          onClick={handleBatchCreateEntities}
          disabled={isLoading}
          className="bg-blue-500 text-white p-2 rounded disabled:opacity-50"
        >
          {isLoading ? 'Creating...' : 'Batch Create Entities'}
        </button>
        
        <button
          onClick={handleBatchDelete}
          disabled={isLoading || selectedEntityIds.length === 0}
          className="bg-red-500 text-white p-2 rounded disabled:opacity-50"
        >
          {isLoading ? 'Deleting...' : `Delete Selected (${selectedEntityIds.length})`}
        </button>
      </div>

      <div className="text-sm text-gray-600">
        Selected entities: {selectedEntityIds.join(', ')}
      </div>
    </div>
  );
}
```

## 缓存系统

### 缓存配置

```typescript
// 缓存配置
const CACHE_CONFIG = {
  TTL: 5 * 60 * 1000,        // 5分钟缓存时间
  MAX_SIZE: 100,             // 最大缓存条目数
  CLEANUP_INTERVAL: 60000    // 清理间隔1分钟
};
```

### 缓存使用

```tsx
import { useGraphMemoryApi } from '@/hooks/useGraphMemoryApi';

export default function CachedDataComponent() {
  const { fetchGraphMemories, getCacheStats, clearCache } = useGraphMemoryApi();

  const loadDataWithCache = async () => {
    // 使用缓存（默认行为）
    const data = await fetchGraphMemories(filters, { useCache: true });
    
    // 强制刷新，跳过缓存
    const freshData = await fetchGraphMemories(filters, { useCache: false });
  };

  const handleClearCache = () => {
    clearCache();
    console.log('Cache cleared');
  };

  const cacheStats = getCacheStats();
  
  return (
    <div>
      <div>Cache Hit Rate: {cacheStats.hitRate.toFixed(2)}%</div>
      <div>Cache Size: {cacheStats.size}/{cacheStats.maxSize}</div>
      <button onClick={handleClearCache}>Clear Cache</button>
    </div>
  );
}
```

## 错误处理

### 错误类型

```typescript
enum GraphMemoryErrorType {
  NETWORK_ERROR = 'NETWORK_ERROR',
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  PERMISSION_ERROR = 'PERMISSION_ERROR',
  NOT_FOUND_ERROR = 'NOT_FOUND_ERROR',
  RATE_LIMIT_ERROR = 'RATE_LIMIT_ERROR',
  UNKNOWN_ERROR = 'UNKNOWN_ERROR'
}
```

### 错误处理示例

```tsx
import { useGraphMemoryApi } from '@/hooks/useGraphMemoryApi';

export default function ErrorHandlingExample() {
  const { fetchGraphMemories, error, clearError, isLoading } = useGraphMemoryApi();

  const handleLoadWithErrorHandling = async () => {
    try {
      clearError(); // 清除之前的错误
      const data = await fetchGraphMemories();
      // 处理成功情况
    } catch (err) {
      // 错误已经被Hook捕获并设置到error状态中
      console.error('Operation failed:', err);
      
      // 根据错误类型进行不同处理
      if (err.type === 'NETWORK_ERROR') {
        // 网络错误，可以重试
        setTimeout(() => handleLoadWithErrorHandling(), 5000);
      } else if (err.type === 'PERMISSION_ERROR') {
        // 权限错误，跳转到登录页
        window.location.href = '/login';
      }
    }
  };

  return (
    <div>
      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          <div className="font-bold">Error:</div>
          <div>{error}</div>
          <button 
            onClick={clearError}
            className="mt-2 bg-red-500 text-white px-2 py-1 rounded text-sm"
          >
            Dismiss
          </button>
        </div>
      )}
      
      <button 
        onClick={handleLoadWithErrorHandling}
        disabled={isLoading}
        className="bg-blue-500 text-white p-2 rounded disabled:opacity-50"
      >
        {isLoading ? 'Loading...' : 'Load Data'}
      </button>
    </div>
  );
}
```

## 性能优化

### 请求去重

```typescript
// Hook内部实现请求去重
const requestCache = new Map();

const deduplicatedRequest = async (key: string, requestFn: () => Promise<any>) => {
  if (requestCache.has(key)) {
    return requestCache.get(key);
  }
  
  const promise = requestFn();
  requestCache.set(key, promise);
  
  try {
    const result = await promise;
    return result;
  } finally {
    requestCache.delete(key);
  }
};
```

### 分页加载

```tsx
import { useGraphMemoryApi } from '@/hooks/useGraphMemoryApi';

export default function PaginatedData() {
  const { fetchGraphMemories, isLoading } = useGraphMemoryApi();
  const [data, setData] = useState({ nodes: [], edges: [] });
  const [hasMore, setHasMore] = useState(true);

  const loadMore = async () => {
    try {
      const newData = await fetchGraphMemories(filters, {
        loadMore: true,
        pageSize: 50
      });
      
      setData(prev => ({
        nodes: [...prev.nodes, ...newData.nodes],
        edges: [...prev.edges, ...newData.edges]
      }));
      
      setHasMore(newData.nodes.length === 50); // 如果返回数据少于pageSize，说明没有更多数据
    } catch (err) {
      console.error('Failed to load more data:', err);
    }
  };

  return (
    <div>
      <div>Loaded: {data.nodes.length} nodes, {data.edges.length} edges</div>
      {hasMore && (
        <button 
          onClick={loadMore} 
          disabled={isLoading}
          className="bg-blue-500 text-white p-2 rounded disabled:opacity-50"
        >
          {isLoading ? 'Loading...' : 'Load More'}
        </button>
      )}
    </div>
  );
}
```

## 最佳实践

1. **合理使用缓存**: 对于频繁访问的数据启用缓存，对于实时性要求高的数据跳过缓存
2. **错误处理**: 始终处理错误状态，提供用户友好的错误信息
3. **加载状态**: 显示加载状态，提升用户体验
4. **批量操作**: 对于大量数据操作，使用批量API减少网络请求
5. **分页加载**: 对于大数据集，使用分页避免一次性加载过多数据
6. **请求去重**: Hook内部已实现，无需额外处理
7. **内存管理**: 定期清理缓存，避免内存泄漏

## 故障排除

### 常见问题

1. **缓存数据过期**: 使用 `clearCache()` 或设置 `useCache: false`
2. **请求超时**: 调整 `timeout` 配置或检查网络连接
3. **权限错误**: 检查用户认证状态和API权限
4. **数据格式错误**: 验证请求参数格式是否正确
5. **内存占用过高**: 定期清理缓存，减少缓存大小限制

### 调试技巧

```tsx
// 启用调试模式
const { getCacheStats, error, isLoading } = useGraphMemoryApi();

// 监控缓存状态
console.log('Cache Stats:', getCacheStats());

// 监控错误状态
useEffect(() => {
  if (error) {
    console.error('API Error:', error);
  }
}, [error]);

// 监控加载状态
useEffect(() => {
  console.log('Loading state changed:', isLoading);
}, [isLoading]);
```
