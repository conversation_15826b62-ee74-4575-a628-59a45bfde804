# Mem0 API格式修复文档

## 问题描述

原有的Mock API格式与实际的Mem0 Server API格式不一致，导致开发环境与生产环境的API调用存在差异。

## 实际API格式分析

基于 `http://localhost:8000/openapi.json` 的分析，实际Mem0 Server API具有以下特征：

### 1. API端点格式
- 所有记忆相关API都使用 `/v1/` 前缀
- 路径格式：`/v1/memories/`, `/v1/memories/{memory_id}/`, `/v1/memories/search/`
- 批量操作：`/v1/batch/`

### 2. 数据结构差异

#### 记忆对象格式
```typescript
// 实际API格式
interface Mem0Memory {
  id: string;
  memory: string;        // 不是 'text'
  hash?: string;
  user_id?: string;
  agent_id?: string;     // 不是 'app_name'
  run_id?: string;
  created_at?: string;
  updated_at?: string;
  metadata?: Record<string, any>;
  categories?: string[];
  score?: number;
  // 没有 'state' 字段
}
```

#### 创建记忆请求格式
```typescript
// 实际API格式
interface MemoryCreateRequest {
  messages: Message[];   // 必需，不是单个 'text' 字段
  user_id?: string;
  agent_id?: string;
  run_id?: string;
  metadata?: Record<string, any>;
  custom_categories?: Array<Record<string, string>>;
  custom_instructions?: string;
  version?: string;
  includes?: string;
  excludes?: string;
  timestamp?: number;
  enable_graph?: boolean;
  output_format?: string;
}

interface Message {
  role: string;
  content: string | object | object[];
}
```

### 3. 不存在的API
实际Mem0 Server中不存在以下API：
- 统计API (`/stats`)
- 活动日志API (`/activities`)
- 用户管理API (`/users`)

## 修复内容

### 1. 类型定义更新
- **文件**: `types/mem0-api.ts`
- **更新**: 根据OpenAPI规范重新定义所有接口
- **主要变更**:
  - 添加 `Message` 接口
  - 更新 `Mem0Memory` 接口格式
  - 添加 `MemoryCreateRequest`, `UpdateMemoryRequest`, `SearchRequest` 等
  - 移除不存在的类型定义

### 2. Mock数据生成器重构
- **文件**: `src/mocks/data/realMockData.ts`
- **更新**: 生成符合实际API格式的测试数据
- **主要变更**:
  - 记忆对象使用 `memory` 字段而非 `text`
  - 使用 `agent_id` 而非 `app_name`
  - 移除 `state` 字段
  - 添加 `hash` 和 `score` 字段

### 3. MSW Handlers重写
- **文件**: `src/mocks/handlers/realMemories.ts`
- **更新**: 完全重写以匹配实际API格式
- **主要变更**:
  - 所有端点使用 `/v1/` 前缀
  - 请求/响应格式匹配OpenAPI规范
  - 添加FastAPI风格的错误处理
  - 实现批量操作API

### 4. API客户端重构
- **文件**: `lib/mem0-client/realClient.ts`
- **更新**: 创建新的API客户端匹配实际格式
- **主要变更**:
  - 所有方法使用正确的端点路径
  - 请求/响应类型匹配实际API
  - 添加FastAPI错误处理
  - 支持所有实际存在的API功能

### 5. MSW配置更新
- **文件**: `src/mocks/handlers/index.ts`, `src/mocks/browser.ts`
- **更新**: 使用新的handlers并添加调试信息
- **主要变更**:
  - 导入新的realMemoriesHandlers
  - 添加端点信息打印功能
  - 更新MSWProvider显示正确的端点列表

## 验证方法

### 1. 测试页面
创建了 `/test-api` 页面用于验证API功能：
- 健康检查测试
- 记忆获取测试
- 错误处理测试
- 端点列表显示

### 2. 单元测试
现有的Jest测试继续通过，确保没有破坏现有功能。

### 3. 开发服务器验证
启动开发服务器后，MSW会在控制台显示所有可用的API端点。

## API端点对比

### 修复前（错误格式）
```
GET/POST /memories
GET /stats
GET /activities
GET/POST/PUT/DELETE /users
```

### 修复后（正确格式）
```
GET    /health
GET    /v1/memories/
POST   /v1/memories/
GET    /v1/memories/{id}/
PUT    /v1/memories/{id}/
DELETE /v1/memories/{id}/
POST   /v1/memories/search/
DELETE /v1/memories/
PUT    /v1/batch/
DELETE /v1/batch/
POST   /reset
```

## 向后兼容性

为了保持向后兼容性：
- 保留了原有的API客户端文件
- 新的API客户端提供了兼容性导出
- 现有的hooks和组件可以逐步迁移

## 下一步

1. 更新现有的hooks以使用新的API客户端
2. 更新组件以适应新的数据格式
3. 移除不再需要的旧API相关代码
4. 完善错误处理和用户体验

## 总结

此次修复确保了Mock API与实际Mem0 Server API的完全一致性，为后续的开发和部署提供了可靠的基础。所有的API调用现在都使用正确的格式，避免了开发环境与生产环境的差异。
