import { http, HttpResponse } from 'msw';
import { mockActivities, paginateData } from '../data/mockData';

// 支持多个API base URL
const API_BASES = [
  'http://localhost:8765/api/v1',
  'http://localhost:8000/v1',
  process.env.NEXT_PUBLIC_API_URL ? process.env.NEXT_PUBLIC_API_URL + '/v1' : null,
  process.env.NEXT_PUBLIC_MEM0_API_URL
].filter((url): url is string => Boolean(url));

// 创建所有base URL的handlers
const createHandlersForBase = (baseUrl: string) => [
  // Get activities with pagination and filtering
  http.get(`${baseUrl}/activities`, ({ request }) => {
    const url = new URL(request.url);
    const userId = url.searchParams.get('user_id');
    const limit = parseInt(url.searchParams.get('limit') || '20');
    const offset = parseInt(url.searchParams.get('offset') || '0');
    const operation = url.searchParams.get('operation'); // 可选的操作类型过滤
    const memoryId = url.searchParams.get('memory_id'); // 可选的记忆ID过滤

    if (!userId) {
      return HttpResponse.json(
        { error: 'Missing user_id parameter', message: 'user_id is required' },
        { status: 400 }
      );
    }

    // 过滤活动数据
    let filteredActivities = mockActivities.filter(activity => activity.user_id === userId);

    // 按操作类型过滤
    if (operation) {
      filteredActivities = filteredActivities.filter(activity => activity.operation === operation);
    }

    // 按记忆ID过滤
    if (memoryId) {
      filteredActivities = filteredActivities.filter(activity => activity.memory_id === memoryId);
    }

    const result = paginateData(filteredActivities, limit, offset);

    return HttpResponse.json({
      activities: result.items,
      total: result.total,
      limit: result.limit,
      offset: result.offset
    });
  }),

  // Get activity by ID
  http.get(`${baseUrl}/activities/:id`, ({ params }) => {
    const { id } = params;
    const activity = mockActivities.find(a => a.id === id);

    if (!activity) {
      return HttpResponse.json(
        { error: 'Activity not found', message: `Activity with id ${id} not found` },
        { status: 404 }
      );
    }

    return HttpResponse.json(activity);
  }),

  // Get activity summary/stats
  http.get(`${baseUrl}/activities/summary`, ({ request }) => {
    const url = new URL(request.url);
    const userId = url.searchParams.get('user_id');
    const period = url.searchParams.get('period') || '24h'; // 24h, 7d, 30d

    if (!userId) {
      return HttpResponse.json(
        { error: 'Missing user_id parameter', message: 'user_id is required' },
        { status: 400 }
      );
    }

    // 计算时间范围
    const now = new Date();
    let startTime: Date;

    switch (period) {
      case '7d':
        startTime = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        break;
      case '30d':
        startTime = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
        break;
      default: // 24h
        startTime = new Date(now.getTime() - 24 * 60 * 60 * 1000);
    }

    // 过滤指定时间范围内的活动
    const periodActivities = mockActivities.filter(activity =>
      activity.user_id === userId &&
      new Date(activity.timestamp) >= startTime
    );

    // 按操作类型统计
    const operationStats = periodActivities.reduce((stats, activity) => {
      stats[activity.operation] = (stats[activity.operation] || 0) + 1;
      return stats;
    }, {} as Record<string, number>);

    // 按应用统计
    const appStats = periodActivities.reduce((stats, activity) => {
      const appName = activity.app_name || 'unknown';
      stats[appName] = (stats[appName] || 0) + 1;
      return stats;
    }, {} as Record<string, number>);

    // 计算平均响应时间
    const avgResponseTime = periodActivities.length > 0
      ? Math.round(periodActivities.reduce((sum, activity) => sum + (activity.response_time || 0), 0) / periodActivities.length)
      : 0;

    return HttpResponse.json({
      period,
      user_id: userId,
      total_activities: periodActivities.length,
      operation_stats: operationStats,
      app_stats: appStats,
      avg_response_time: avgResponseTime,
      success_rate: periodActivities.filter(a => a.metadata?.success !== false).length / periodActivities.length,
      most_active_hour: Math.floor(Math.random() * 24), // 模拟最活跃小时
      timeline: periodActivities.slice(0, 10) // 最近10个活动
    });
  }),

  // Real-time activity stream (Server-Sent Events simulation)
  http.get(`${baseUrl}/activities/stream`, ({ request }) => {
    const url = new URL(request.url);
    const userId = url.searchParams.get('user_id');

    if (!userId) {
      return HttpResponse.json(
        { error: 'Missing user_id parameter', message: 'user_id is required' },
        { status: 400 }
      );
    }

    // 模拟实时活动流（返回最近的活动）
    const recentActivities = mockActivities
      .filter(activity => activity.user_id === userId)
      .slice(0, 5);

    return HttpResponse.json({
      stream_id: `stream_${Date.now()}`,
      user_id: userId,
      activities: recentActivities,
      next_poll_interval: 5000 // 建议5秒后再次轮询
    });
  })
];

// 为所有API base创建handlers
export const activityHandlers = API_BASES.flatMap(createHandlersForBase);
