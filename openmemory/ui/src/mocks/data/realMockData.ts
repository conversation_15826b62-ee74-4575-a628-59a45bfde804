/**
 * 真实Mem0 API格式的Mock数据生成器
 * 基于http://localhost:8000/openapi.json规范
 */

import { Mem0Memory, Message } from '@/types/mem0-api';

// 模拟用户和代理ID
export const mockUserIds = ['user_1', 'user_2', 'user_3', 'user_4', 'user_5'];
export const mockAgentIds = ['agent_1', 'agent_2', 'agent_3'];
export const mockRunIds = ['run_1', 'run_2', 'run_3', 'run_4'];

// 模拟分类
export const mockCategories = [
  'work', 'personal', 'important', 'urgent', 'project',
  'meeting', 'development', 'learning', 'health', 'finance',
  'travel', 'food', 'entertainment', 'family', 'friends'
];

// 模拟记忆内容
const memoryContents = [
  "I prefer working in quiet environments with minimal distractions",
  "My favorite programming language is TypeScript for its type safety",
  "I usually have coffee at 9 AM and 2 PM every day",
  "I'm allergic to shellfish and need to avoid seafood restaurants",
  "My preferred meeting time is between 10 AM and 4 PM",
  "I like to use dark themes in all my development tools",
  "I'm working on a React project with Next.js framework",
  "My budget for lunch is usually around $15-20",
  "I prefer async communication over synchronous meetings",
  "I'm learning about machine learning and AI development",
  "I use VS Code as my primary code editor",
  "I prefer to work from home on Mondays and Fridays",
  "My team uses Slack for internal communication",
  "I'm interested in sustainable technology and green computing",
  "I usually take breaks every 2 hours during work",
  "I prefer written documentation over video tutorials",
  "My current project involves building a memory management system",
  "I like to review code in the morning when I'm most focused",
  "I use Git for version control and prefer feature branches",
  "I'm interested in attending tech conferences about AI and ML"
];

// 生成模拟记忆数据
export const generateMockMemories = (count: number = 20): Mem0Memory[] => {
  const categories = ['personal', 'work', 'preferences', 'health', 'technology', 'learning'];
  
  return Array.from({ length: count }, (_, index) => {
    const userId = mockUserIds[index % mockUserIds.length];
    const agentId = Math.random() > 0.5 ? mockAgentIds[index % mockAgentIds.length] : undefined;
    const runId = Math.random() > 0.7 ? mockRunIds[index % mockRunIds.length] : undefined;
    
    return {
      id: `mem_${String(index + 1).padStart(8, '0')}`,
      memory: memoryContents[index % memoryContents.length],
      hash: `hash_${Math.random().toString(36).substring(2, 15)}`,
      user_id: userId,
      agent_id: agentId,
      run_id: runId,
      custom_categories: mockCategories.slice(0, Math.floor(Math.random() * 3) + 1),
      created_at: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString(),
      updated_at: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000).toISOString(),
      metadata: {
        source: ['api', 'web', 'mobile'][Math.floor(Math.random() * 3)],
        confidence: Math.random() * 0.3 + 0.7, // 0.7-1.0
        version: 'v1'
      },
      categories: categories.slice(0, Math.floor(Math.random() * 3) + 1),
      score: Math.random() * 0.3 + 0.7 // 0.7-1.0
    };
  });
};

// 预生成的记忆数据
export const mockMemories = generateMockMemories(50);

// 生成模拟消息数据
export const generateMockMessages = (count: number = 5): Message[] => {
  const roles = ['user', 'assistant'];
  const contents = [
    "Hello, I need help with my project",
    "I can help you with that. What specific area do you need assistance with?",
    "I'm working on a React application and need to implement state management",
    "For React state management, I'd recommend using Redux Toolkit or Zustand. What's your use case?",
    "I need to manage user authentication and API calls",
    "For authentication, you might want to consider using NextAuth.js or Auth0",
    "Can you help me understand the best practices for API integration?",
    "Sure! Here are some key principles for API integration...",
    "What about error handling in React applications?",
    "Error handling is crucial. You should implement error boundaries and proper try-catch blocks"
  ];
  
  return Array.from({ length: count }, (_, index) => ({
    role: roles[index % roles.length],
    content: contents[index % contents.length]
  }));
};

// 搜索记忆功能
export const searchMemories = (query: string, memories: Mem0Memory[]): Mem0Memory[] => {
  if (!query.trim()) return memories;
  
  const lowercaseQuery = query.toLowerCase();
  return memories.filter(memory => 
    memory.memory.toLowerCase().includes(lowercaseQuery) ||
    memory.categories?.some(cat => cat.toLowerCase().includes(lowercaseQuery)) ||
    memory.user_id?.toLowerCase().includes(lowercaseQuery) ||
    memory.agent_id?.toLowerCase().includes(lowercaseQuery)
  );
};

// 过滤记忆功能
export const filterMemories = (
  memories: Mem0Memory[], 
  filters: Record<string, any>
): Mem0Memory[] => {
  return memories.filter(memory => {
    // 按用户ID过滤
    if (filters.user_id && memory.user_id !== filters.user_id) {
      return false;
    }
    
    // 按代理ID过滤
    if (filters.agent_id && memory.agent_id !== filters.agent_id) {
      return false;
    }
    
    // 按运行ID过滤
    if (filters.run_id && memory.run_id !== filters.run_id) {
      return false;
    }
    
    // 按分类过滤
    if (filters.categories && Array.isArray(filters.categories)) {
      const hasCategory = filters.categories.some((cat: string) => 
        memory.categories?.includes(cat)
      );
      if (!hasCategory) return false;
    }
    
    return true;
  });
};

// 分页功能
export const paginateData = <T>(
  data: T[], 
  limit: number = 100, 
  offset: number = 0
): { 
  items: T[]; 
  total: number; 
  limit: number; 
  offset: number; 
  hasMore: boolean 
} => {
  const items = data.slice(offset, offset + limit);
  return {
    items,
    total: data.length,
    limit,
    offset,
    hasMore: offset + limit < data.length
  };
};

// 健康检查响应
export const generateHealthResponse = () => ({
  status: 'healthy',
  timestamp: new Date().toISOString(),
  version: '1.0.0',
  uptime: Math.floor(Math.random() * 86400), // 随机运行时间（秒）
});

// 模拟批量操作响应
export const generateBatchResponse = (successCount: number, failedCount: number = 0) => ({
  success_count: successCount,
  failed_count: failedCount,
  errors: failedCount > 0 ? Array.from({ length: failedCount }, (_, i) => ({
    memory_id: `mem_error_${i}`,
    error: 'Memory not found'
  })) : []
});
