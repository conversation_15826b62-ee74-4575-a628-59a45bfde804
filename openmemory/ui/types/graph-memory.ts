/**
 * Graph Memory 类型定义
 * 基于 Mem0 Graph Memory API 和 React Flow 数据格式
 */

import { Node, Edge } from 'reactflow'
import { Mem0Memory } from './mem0-api'

// ============================================================================
// Mem0 Graph Memory API 类型
// ============================================================================

/**
 * Mem0 Graph Memory 实体类型
 */
export interface Mem0GraphEntity {
  id: string
  name: string
  type: string
  description?: string
  properties?: Record<string, any>
  metadata?: Record<string, any>
  created_at?: string
  updated_at?: string
}

/**
 * Mem0 Graph Memory 关系类型
 */
export interface Mem0GraphRelation {
  id: string
  source_entity_id: string
  target_entity_id: string
  relation_type: string
  description?: string
  weight?: number
  properties?: Record<string, any>
  metadata?: Record<string, any>
  created_at?: string
  updated_at?: string
}

/**
 * Mem0 Graph Memory API 响应
 */
export interface Mem0GraphMemoryResponse {
  entities: Mem0GraphEntity[]
  relations: Mem0GraphRelation[]
  metadata?: {
    total_entities: number
    total_relations: number
    graph_density?: number
    active_entities?: number
  }
}

/**
 * Mem0 Graph Memory 搜索请求
 */
export interface Mem0GraphSearchRequest {
  query?: string
  user_id?: string
  agent_id?: string
  run_id?: string
  entity_types?: string[]
  relation_types?: string[]
  limit?: number
  enable_graph: true
  output_format: 'v1.1'
}

// ============================================================================
// React Flow 兼容类型
// ============================================================================

/**
 * Graph Memory 节点数据
 */
export interface GraphNodeData {
  id: string
  label: string
  name?: string
  type: string
  description?: string
  properties?: Record<string, any>
  metadata?: Record<string, any>
  // 时间戳
  created_at?: string
  updated_at?: string
  // 视觉属性
  color?: string
  size?: number
  icon?: string
  // 状态属性
  isSelected?: boolean
  isHighlighted?: boolean
  isActive?: boolean
}

/**
 * Graph Memory 边数据
 */
export interface GraphEdgeData {
  id: string
  label: string
  relation_type: string
  description?: string
  weight?: number
  properties?: Record<string, any>
  metadata?: Record<string, any>
  // 时间戳
  created_at?: string
  updated_at?: string
  // 视觉属性
  color?: string
  width?: number
  style?: 'solid' | 'dashed' | 'dotted'
  // 状态属性
  isSelected?: boolean
  isHighlighted?: boolean
}

/**
 * React Flow 节点类型（扩展）
 */
export type GraphNode = Node<GraphNodeData>

/**
 * React Flow 边类型（扩展）
 */
export type GraphEdge = Edge<GraphEdgeData>

// ============================================================================
// Graph Memory 筛选和视图状态
// ============================================================================

/**
 * Graph Memory 筛选条件
 */
export interface GraphMemoryFilters {
  // 基础筛选
  user_id?: string
  agent_id?: string
  run_id?: string
  
  // 实体筛选
  entity_types?: string[]
  entity_search?: string
  
  // 关系筛选
  relation_types?: string[]
  weight_range?: {
    min: number
    max: number
  }
  
  // 时间筛选
  date_range?: {
    start: string
    end: string
  }
  
  // 高级筛选
  has_description?: boolean
  has_properties?: boolean
  is_active?: boolean
}

/**
 * Graph Memory 视图状态
 */
export interface GraphMemoryViewState {
  // 布局设置
  layout: 'force' | 'hierarchical' | 'circular' | 'grid'
  
  // 视图控制
  zoom: number
  center: { x: number; y: number }
  
  // 显示选项
  show_labels: boolean
  show_edge_labels: boolean
  show_minimap: boolean
  show_controls: boolean
  
  // 交互状态
  is_dragging: boolean
  is_selecting: boolean
  selection_mode: 'single' | 'multiple' | 'box'
}

/**
 * Graph Memory 统计信息
 */
export interface GraphMemoryStats {
  total_entities: number
  total_relations: number
  graph_density: number
  active_entities: number
  entity_types_count: Record<string, number>
  relation_types_count: Record<string, number>
  avg_connections_per_entity: number
  most_connected_entities: Array<{
    id: string
    name: string
    connections: number
  }>
}

// ============================================================================
// Graph Memory 操作历史
// ============================================================================

/**
 * Graph Memory 操作类型
 */
export type GraphMemoryOperationType = 
  | 'entity_create'
  | 'entity_update' 
  | 'entity_delete'
  | 'relation_create'
  | 'relation_update'
  | 'relation_delete'
  | 'graph_layout_change'
  | 'filter_change'
  | 'view_change'

/**
 * Graph Memory 操作历史项
 */
export interface GraphMemoryHistoryItem {
  id: string
  operation: GraphMemoryOperationType
  timestamp: string
  user_id?: string
  target_id: string // 实体或关系ID
  target_type: 'entity' | 'relation' | 'view'
  description: string
  changes?: {
    before?: any
    after?: any
  }
  metadata?: Record<string, any>
}

// ============================================================================
// 批量操作类型
// ============================================================================

/**
 * 批量操作请求
 */
export interface GraphMemoryBatchOperation {
  operation: 'update' | 'delete' | 'create'
  target_type: 'entities' | 'relations'
  targets: string[] // IDs
  data?: Record<string, any>
}

/**
 * 批量操作结果
 */
export interface GraphMemoryBatchResult {
  success: boolean
  processed: number
  failed: number
  errors?: Array<{
    id: string
    error: string
  }>
}

// ============================================================================
// 导出所有类型
// ============================================================================

export type {
  Node as ReactFlowNode,
  Edge as ReactFlowEdge
} from 'reactflow'
