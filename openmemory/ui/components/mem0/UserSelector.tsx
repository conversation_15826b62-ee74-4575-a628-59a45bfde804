import React, { useState } from 'react';
import { Users, Plus, Trash2, User } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '@/components/ui/select';
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogFooter, 
  DialogHeader, 
  DialogTitle 
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useToast } from '@/components/ui/use-toast';
import { useUserManagement } from '@/hooks/useUserManagement';

interface UserSelectorProps {
  value?: string | undefined;
  onChange?: ((userId: string) => void) | undefined;
  className?: string;
}

const UserSelector: React.FC<UserSelectorProps> = ({
  value,
  onChange,
  className
}) => {
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [newUserId, setNewUserId] = useState('');
  const [newUserName, setNewUserName] = useState('');
  const [userToDelete, setUserToDelete] = useState<string>('');
  
  const { toast } = useToast();
  const {
    users,
    currentUserId,
    isLoading,
    setCurrentUserId,
    createUser,
    deleteUser
  } = useUserManagement();

  const handleUserChange = (userId: string) => {
    setCurrentUserId(userId);
    onChange?.(userId);
  };

  const handleCreateUser = async () => {
    if (!newUserId.trim()) {
      toast({
        title: "错误",
        description: "请输入用户ID",
        variant: "destructive"
      });
      return;
    }

    try {
      await createUser(newUserId.trim(), newUserName.trim() || undefined);
      toast({
        title: "成功",
        description: "用户创建成功",
        variant: "default"
      });
      setNewUserId('');
      setNewUserName('');
      setShowCreateDialog(false);
    } catch (error) {
      toast({
        title: "错误",
        description: error instanceof Error ? error.message : "创建用户失败",
        variant: "destructive"
      });
    }
  };

  const handleDeleteUser = async () => {
    if (!userToDelete) return;

    try {
      await deleteUser(userToDelete);
      toast({
        title: "成功",
        description: "用户删除成功",
        variant: "default"
      });
      setUserToDelete('');
      setShowDeleteDialog(false);
    } catch (error) {
      toast({
        title: "错误",
        description: error instanceof Error ? error.message : "删除用户失败",
        variant: "destructive"
      });
    }
  };

  const selectedUser = users.find(user => user.id === (value || currentUserId));

  return (
    <>
      <div className={`space-y-2 ${className}`}>
        <div className="flex items-center justify-between">
          <Label className="text-zinc-400 flex items-center gap-2">
            <Users className="w-4 h-4" />
            用户管理
          </Label>
          <div className="flex gap-1">
            <Button
              size="sm"
              variant="outline"
              onClick={() => setShowCreateDialog(true)}
              className="h-6 w-6 p-0 border-zinc-700 hover:border-zinc-600"
              disabled={isLoading}
            >
              <Plus className="w-3 h-3" />
            </Button>
            <Button
              size="sm"
              variant="outline"
              onClick={() => {
                if (selectedUser && selectedUser.id !== 'default') {
                  setUserToDelete(selectedUser.id);
                  setShowDeleteDialog(true);
                } else {
                  toast({
                    title: "提示",
                    description: "无法删除默认用户",
                    variant: "default"
                  });
                }
              }}
              className="h-6 w-6 p-0 border-zinc-700 hover:border-zinc-600"
              disabled={isLoading || !selectedUser || selectedUser.id === 'default'}
            >
              <Trash2 className="w-3 h-3" />
            </Button>
          </div>
        </div>
        
        <Select 
          value={value || currentUserId || ''} 
          onValueChange={handleUserChange}
          disabled={isLoading}
        >
          <SelectTrigger className="bg-zinc-800 border-zinc-700 text-white">
            <SelectValue placeholder="选择用户">
              {selectedUser && (
                <div className="flex items-center gap-2">
                  <User className="w-4 h-4 text-[#00d4aa]" />
                  <span>{selectedUser.name || selectedUser.id}</span>
                  <span className="text-xs text-zinc-400">
                    ({selectedUser.memory_count} 记忆)
                  </span>
                </div>
              )}
            </SelectValue>
          </SelectTrigger>
          <SelectContent className="bg-zinc-800 border-zinc-700">
            {users.map((user) => (
              <SelectItem key={user.id} value={user.id}>
                <div className="flex items-center gap-2">
                  <User className="w-4 h-4 text-[#00d4aa]" />
                  <span>{user.name || user.id}</span>
                  <span className="text-xs text-zinc-400">
                    ({user.memory_count} 记忆)
                  </span>
                </div>
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {/* 创建用户对话框 */}
      <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
        <DialogContent className="bg-zinc-900 border-zinc-800">
          <DialogHeader>
            <DialogTitle className="text-white">创建新用户</DialogTitle>
            <DialogDescription className="text-zinc-400">
              输入用户ID和可选的显示名称。
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div className="space-y-2">
              <Label className="text-zinc-400">用户ID *</Label>
              <Input
                value={newUserId}
                onChange={(e) => setNewUserId(e.target.value)}
                placeholder="输入唯一的用户ID"
                className="bg-zinc-800 border-zinc-700 text-white"
              />
            </div>
            <div className="space-y-2">
              <Label className="text-zinc-400">显示名称</Label>
              <Input
                value={newUserName}
                onChange={(e) => setNewUserName(e.target.value)}
                placeholder="输入用户显示名称（可选）"
                className="bg-zinc-800 border-zinc-700 text-white"
              />
            </div>
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setShowCreateDialog(false)}
              className="border-zinc-700"
            >
              取消
            </Button>
            <Button
              onClick={handleCreateUser}
              disabled={isLoading || !newUserId.trim()}
              className="bg-[#00d4aa] hover:bg-[#00d4aa]/90 text-black"
            >
              {isLoading ? '创建中...' : '创建用户'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 删除用户确认对话框 */}
      <Dialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <DialogContent className="bg-zinc-900 border-zinc-800">
          <DialogHeader>
            <DialogTitle className="text-white">确认删除用户</DialogTitle>
            <DialogDescription className="text-zinc-400">
              此操作将删除用户 "{userToDelete}" 及其所有记忆数据，此操作不可撤销。
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setShowDeleteDialog(false)}
              className="border-zinc-700"
            >
              取消
            </Button>
            <Button
              variant="destructive"
              onClick={handleDeleteUser}
              disabled={isLoading}
            >
              {isLoading ? '删除中...' : '确认删除'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default UserSelector;
