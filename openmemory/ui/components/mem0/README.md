# Mem0 Components

This directory contains components specifically designed for the Mem0 core memory management interface.

## Components

- `Mem0StatsDashboard.tsx` - Four-card statistics dashboard
- `ActivityTimeline.tsx` - Memory operation history timeline
- `QuickActions.tsx` - Quick action buttons for common operations

## Design Guidelines

- Maintain black theme consistency with existing components
- Use Mem0 brand color (#00d4aa) as accent color
- Follow existing component patterns and naming conventions
- Ensure responsive design for all screen sizes

## Development Notes

All components in this directory should:
1. Use TypeScript with strict typing
2. Follow the existing component structure
3. Include proper error handling and loading states
4. Be tested with Jest and React Testing Library
