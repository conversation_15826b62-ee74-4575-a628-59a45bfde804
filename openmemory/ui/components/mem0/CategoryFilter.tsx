import React, { useState } from 'react';
import { Tag, X, ChevronDown, Check } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Popover, 
  PopoverContent, 
  PopoverTrigger 
} from '@/components/ui/popover';
import { 
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList
} from '@/components/ui/command';

interface CategoryFilterProps {
  categories: string[];
  selectedCategories: string[];
  onCategoryChange: (selectedCategories: string[]) => void;
  placeholder?: string;
  className?: string;
}

const CategoryFilter: React.FC<CategoryFilterProps> = ({
  categories,
  selectedCategories,
  onCategoryChange,
  placeholder = "选择分类筛选...",
  className
}) => {
  const [open, setOpen] = useState(false);

  const handleCategoryToggle = (category: string) => {
    const newSelected = selectedCategories.includes(category)
      ? selectedCategories.filter(c => c !== category)
      : [...selectedCategories, category];
    
    onCategoryChange(newSelected);
  };

  const handleRemoveCategory = (category: string) => {
    const newSelected = selectedCategories.filter(c => c !== category);
    onCategoryChange(newSelected);
  };

  const handleClearAll = () => {
    onCategoryChange([]);
  };

  return (
    <div className={`space-y-3 ${className}`}>
      {/* 分类选择器 */}
      <div className="flex items-center gap-2">
        <Tag className="w-4 h-4 text-zinc-400" />
        <span className="text-sm text-zinc-400">分类筛选:</span>
        
        <Popover open={open} onOpenChange={setOpen}>
          <PopoverTrigger asChild>
            <Button
              variant="outline"
              role="combobox"
              aria-expanded={open}
              className="w-[200px] justify-between border-zinc-700 hover:border-zinc-600 bg-zinc-800"
            >
              <span className="text-zinc-300">
                {selectedCategories.length > 0 
                  ? `已选择 ${selectedCategories.length} 个分类`
                  : placeholder
                }
              </span>
              <ChevronDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-[200px] p-0 bg-zinc-800 border-zinc-700">
            <Command className="bg-zinc-800">
              <CommandInput 
                placeholder="搜索分类..." 
                className="border-0 bg-zinc-800 text-white"
              />
              <CommandList>
                <CommandEmpty className="text-zinc-400 text-center py-6">
                  未找到分类
                </CommandEmpty>
                <CommandGroup>
                  {categories.map((category) => (
                    <CommandItem
                      key={category}
                      value={category}
                      onSelect={() => handleCategoryToggle(category)}
                      className="text-zinc-300 hover:bg-zinc-700"
                    >
                      <Check
                        className={`mr-2 h-4 w-4 ${
                          selectedCategories.includes(category) 
                            ? "opacity-100 text-[#00d4aa]" 
                            : "opacity-0"
                        }`}
                      />
                      <Tag className="w-3 h-3 mr-2 text-zinc-500" />
                      {category}
                    </CommandItem>
                  ))}
                </CommandGroup>
              </CommandList>
            </Command>
          </PopoverContent>
        </Popover>

        {selectedCategories.length > 0 && (
          <Button
            variant="ghost"
            size="sm"
            onClick={handleClearAll}
            className="text-zinc-400 hover:text-white h-8 px-2"
          >
            清除全部
          </Button>
        )}
      </div>

      {/* 已选择的分类标签 */}
      {selectedCategories.length > 0 && (
        <div className="flex flex-wrap gap-2">
          {selectedCategories.map((category) => (
            <Badge
              key={category}
              variant="outline"
              className="bg-[#00d4aa]/10 border-[#00d4aa]/20 text-[#00d4aa] hover:bg-[#00d4aa]/20 transition-colors"
            >
              <Tag className="w-3 h-3 mr-1" />
              {category}
              <button
                onClick={() => handleRemoveCategory(category)}
                className="ml-2 hover:bg-[#00d4aa]/20 rounded-full p-0.5 transition-colors"
              >
                <X className="w-3 h-3" />
              </button>
            </Badge>
          ))}
        </div>
      )}

      {/* 统计信息 */}
      <div className="text-xs text-zinc-500">
        {categories.length > 0 ? (
          <>
            共 {categories.length} 个分类可选
            {selectedCategories.length > 0 && (
              <span className="ml-2">• 已筛选 {selectedCategories.length} 个</span>
            )}
          </>
        ) : (
          '暂无分类数据'
        )}
      </div>
    </div>
  );
};

export default CategoryFilter;
