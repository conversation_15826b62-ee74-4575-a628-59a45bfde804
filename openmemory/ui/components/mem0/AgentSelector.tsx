import React, { useState } from 'react';
import { Bot, Plus, Trash2, Cpu } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '@/components/ui/select';
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogFooter, 
  DialogHeader, 
  DialogTitle 
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { useToast } from '@/components/ui/use-toast';
import { useAgentManagement } from '@/hooks/useAgentManagement';

interface AgentSelectorProps {
  value?: string | undefined;
  onChange?: ((agentId: string) => void) | undefined;
  className?: string;
}

const AgentSelector: React.FC<AgentSelectorProps> = ({
  value,
  onChange,
  className
}) => {
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [newAgentId, setNewAgentId] = useState('');
  const [newAgentName, setNewAgentName] = useState('');
  const [newAgentDescription, setNewAgentDescription] = useState('');
  const [newAgentType, setNewAgentType] = useState('custom');
  const [agentToDelete, setAgentToDelete] = useState<string>('');
  
  const { toast } = useToast();
  const {
    agents,
    currentAgentId,
    isLoading,
    setCurrentAgentId,
    createAgent,
    deleteAgent
  } = useAgentManagement();

  const handleAgentChange = (agentId: string) => {
    setCurrentAgentId(agentId);
    onChange?.(agentId);
  };

  const handleCreateAgent = async () => {
    if (!newAgentId.trim()) {
      toast({
        title: "错误",
        description: "请输入智能体ID",
        variant: "destructive"
      });
      return;
    }

    try {
      await createAgent(
        newAgentId.trim(), 
        newAgentName.trim() || undefined,
        newAgentDescription.trim() || undefined,
        newAgentType
      );
      toast({
        title: "成功",
        description: "智能体创建成功",
        variant: "default"
      });
      setNewAgentId('');
      setNewAgentName('');
      setNewAgentDescription('');
      setNewAgentType('custom');
      setShowCreateDialog(false);
    } catch (error) {
      toast({
        title: "错误",
        description: error instanceof Error ? error.message : "创建智能体失败",
        variant: "destructive"
      });
    }
  };

  const handleDeleteAgent = async () => {
    if (!agentToDelete) return;

    try {
      await deleteAgent(agentToDelete);
      toast({
        title: "成功",
        description: "智能体删除成功",
        variant: "default"
      });
      setAgentToDelete('');
      setShowDeleteDialog(false);
    } catch (error) {
      toast({
        title: "错误",
        description: error instanceof Error ? error.message : "删除智能体失败",
        variant: "destructive"
      });
    }
  };

  const selectedAgent = agents.find(agent => agent.id === (value || currentAgentId));

  const getAgentIcon = (type?: string) => {
    switch (type) {
      case 'system':
        return <Cpu className="w-4 h-4 text-blue-400" />;
      default:
        return <Bot className="w-4 h-4 text-[#00d4aa]" />;
    }
  };

  return (
    <>
      <div className={`space-y-2 ${className}`}>
        <div className="flex items-center justify-between">
          <Label className="text-zinc-400 flex items-center gap-2">
            <Bot className="w-4 h-4" />
            智能体管理
          </Label>
          <div className="flex gap-1">
            <Button
              size="sm"
              variant="outline"
              onClick={() => setShowCreateDialog(true)}
              className="h-6 w-6 p-0 border-zinc-700 hover:border-zinc-600"
              disabled={isLoading}
            >
              <Plus className="w-3 h-3" />
            </Button>
            <Button
              size="sm"
              variant="outline"
              onClick={() => {
                if (selectedAgent && selectedAgent.id !== 'default') {
                  setAgentToDelete(selectedAgent.id);
                  setShowDeleteDialog(true);
                } else {
                  toast({
                    title: "提示",
                    description: "无法删除默认智能体",
                    variant: "default"
                  });
                }
              }}
              className="h-6 w-6 p-0 border-zinc-700 hover:border-zinc-600"
              disabled={isLoading || !selectedAgent || selectedAgent.id === 'default'}
            >
              <Trash2 className="w-3 h-3" />
            </Button>
          </div>
        </div>
        
        <Select 
          value={value || currentAgentId || ''} 
          onValueChange={handleAgentChange}
          disabled={isLoading}
        >
          <SelectTrigger className="bg-zinc-800 border-zinc-700 text-white">
            <SelectValue placeholder="选择智能体">
              {selectedAgent && (
                <div className="flex items-center gap-2">
                  {getAgentIcon(selectedAgent.type)}
                  <span>{selectedAgent.name}</span>
                  <span className="text-xs text-zinc-400">
                    ({selectedAgent.memory_count} 记忆)
                  </span>
                </div>
              )}
            </SelectValue>
          </SelectTrigger>
          <SelectContent className="bg-zinc-800 border-zinc-700">
            {agents.map((agent) => (
              <SelectItem key={agent.id} value={agent.id}>
                <div className="flex items-center gap-2">
                  {getAgentIcon(agent.type)}
                  <div className="flex flex-col">
                    <span>{agent.name}</span>
                    {agent.description && (
                      <span className="text-xs text-zinc-500">{agent.description}</span>
                    )}
                  </div>
                  <span className="text-xs text-zinc-400">
                    ({agent.memory_count} 记忆)
                  </span>
                </div>
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {/* 创建智能体对话框 */}
      <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
        <DialogContent className="bg-zinc-900 border-zinc-800">
          <DialogHeader>
            <DialogTitle className="text-white">创建新智能体</DialogTitle>
            <DialogDescription className="text-zinc-400">
              输入智能体ID和相关信息。
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div className="space-y-2">
              <Label className="text-zinc-400">智能体ID *</Label>
              <Input
                value={newAgentId}
                onChange={(e) => setNewAgentId(e.target.value)}
                placeholder="输入唯一的智能体ID"
                className="bg-zinc-800 border-zinc-700 text-white"
              />
            </div>
            <div className="space-y-2">
              <Label className="text-zinc-400">智能体名称</Label>
              <Input
                value={newAgentName}
                onChange={(e) => setNewAgentName(e.target.value)}
                placeholder="输入智能体显示名称（可选）"
                className="bg-zinc-800 border-zinc-700 text-white"
              />
            </div>
            <div className="space-y-2">
              <Label className="text-zinc-400">智能体类型</Label>
              <Select value={newAgentType} onValueChange={setNewAgentType}>
                <SelectTrigger className="bg-zinc-800 border-zinc-700 text-white">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent className="bg-zinc-800 border-zinc-700">
                  <SelectItem value="custom">自定义智能体</SelectItem>
                  <SelectItem value="assistant">助手智能体</SelectItem>
                  <SelectItem value="chatbot">聊天机器人</SelectItem>
                  <SelectItem value="analyzer">分析智能体</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label className="text-zinc-400">智能体描述</Label>
              <Textarea
                value={newAgentDescription}
                onChange={(e) => setNewAgentDescription(e.target.value)}
                placeholder="输入智能体描述（可选）"
                className="bg-zinc-800 border-zinc-700 text-white"
                rows={3}
              />
            </div>
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setShowCreateDialog(false)}
              className="border-zinc-700"
            >
              取消
            </Button>
            <Button
              onClick={handleCreateAgent}
              disabled={isLoading || !newAgentId.trim()}
              className="bg-[#00d4aa] hover:bg-[#00d4aa]/90 text-black"
            >
              {isLoading ? '创建中...' : '创建智能体'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 删除智能体确认对话框 */}
      <Dialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <DialogContent className="bg-zinc-900 border-zinc-800">
          <DialogHeader>
            <DialogTitle className="text-white">确认删除智能体</DialogTitle>
            <DialogDescription className="text-zinc-400">
              此操作将删除智能体 "{agentToDelete}" 及其所有记忆数据，此操作不可撤销。
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setShowDeleteDialog(false)}
              className="border-zinc-700"
            >
              取消
            </Button>
            <Button
              variant="destructive"
              onClick={handleDeleteAgent}
              disabled={isLoading}
            >
              {isLoading ? '删除中...' : '确认删除'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default AgentSelector;
