import React from 'react';
import {
  Plus,
  Edit,
  Trash2,
  User,
  Bot,
  Package,
  Tag,
  Clock,
  Search
} from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { UIActivityItem } from '@/types/mem0-api';

interface TimelineItemProps {
  data: UIActivityItem;
  isLast?: boolean;
}

const TimelineItem: React.FC<TimelineItemProps> = ({ data, isLast = false }) => {
  const getOperationIcon = (operation: string) => {
    switch (operation.toUpperCase()) {
      case 'ADD':
        return <Plus className="w-4 h-4 text-green-400" />;
      case 'UPDATE':
        return <Edit className="w-4 h-4 text-blue-400" />;
      case 'DELETE':
        return <Trash2 className="w-4 h-4 text-red-400" />;
      case 'SEARCH':
        return <Search className="w-4 h-4 text-purple-400" />;
      case 'GRAPH_CREATE':
        return <Package className="w-4 h-4 text-cyan-400" />;
      default:
        return <Clock className="w-4 h-4 text-zinc-400" />;
    }
  };

  const getOperationText = (operation: string) => {
    switch (operation.toUpperCase()) {
      case 'ADD':
        return '添加了记忆';
      case 'UPDATE':
        return '更新了记忆';
      case 'DELETE':
        return '删除了记忆';
      case 'SEARCH':
        return '搜索了记忆';
      case 'GRAPH_CREATE':
        return '创建了图记忆';
      default:
        return '操作了记忆';
    }
  };

  const getOperationColor = (operation: string) => {
    switch (operation.toUpperCase()) {
      case 'ADD':
        return 'bg-green-400/10 border-green-400/20';
      case 'UPDATE':
        return 'bg-blue-400/10 border-blue-400/20';
      case 'DELETE':
        return 'bg-red-400/10 border-red-400/20';
      case 'SEARCH':
        return 'bg-purple-400/10 border-purple-400/20';
      case 'GRAPH_CREATE':
        return 'bg-cyan-400/10 border-cyan-400/20';
      default:
        return 'bg-zinc-400/10 border-zinc-400/20';
    }
  };

  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / (1000 * 60));
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

    if (diffMins < 1) {
      return '刚刚';
    } else if (diffMins < 60) {
      return `${diffMins}分钟前`;
    } else if (diffHours < 24) {
      return `${diffHours}小时前`;
    } else if (diffDays < 7) {
      return `${diffDays}天前`;
    } else {
      return date.toLocaleDateString('zh-CN', {
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      });
    }
  };

  const truncateText = (text: string, maxLength: number = 100) => {
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength) + '...';
  };

  return (
    <div className="relative">
      {/* 时间线连接线 */}
      {!isLast && (
        <div className="absolute left-6 top-12 w-px h-full bg-zinc-800"></div>
      )}
      
      <div className="flex items-start gap-4 p-4 hover:bg-zinc-800/50 transition-colors">
        {/* 操作图标 */}
        <div className={`flex-shrink-0 w-8 h-8 rounded-full border-2 flex items-center justify-center ${getOperationColor(data.operation)}`}>
          {getOperationIcon(data.operation)}
        </div>

        {/* 内容区域 */}
        <div className="flex-1 min-w-0">
          {/* 操作描述和时间 */}
          <div className="flex items-center justify-between mb-2">
            <div className="flex items-center gap-2">
              <span className="text-white text-sm font-medium">
                {getOperationText(data.operation)}
              </span>
              {data.memory_id && (
                <span className="text-xs text-zinc-500 font-mono">
                  #{data.memory_id.slice(-8)}
                </span>
              )}
            </div>
            <span className="text-xs text-zinc-500 flex-shrink-0">
              {formatTimestamp(data.timestamp)}
            </span>
          </div>

          {/* 活动详情 */}
          {data.details && (
            <div className="mb-3">
              <p className="text-sm text-zinc-300 leading-relaxed">
                {truncateText(data.details)}
              </p>
            </div>
          )}

          {/* 上下文信息 */}
          <div className="flex items-center gap-3 mb-2 text-xs">
            {data.user_id && (
              <div className="flex items-center gap-1 text-zinc-400">
                <User className="w-3 h-3" />
                <span>{data.user_id}</span>
              </div>
            )}
            {data.status && (
              <div className="flex items-center gap-1 text-zinc-400">
                <Package className="w-3 h-3" />
                <span className={`px-2 py-1 rounded text-xs ${
                  data.status === 'success' ? 'bg-green-400/10 text-green-400' :
                  data.status === 'error' ? 'bg-red-400/10 text-red-400' :
                  'bg-yellow-400/10 text-yellow-400'
                }`}>
                  {data.status}
                </span>
              </div>
            )}
            {data.response_time && (
              <div className="flex items-center gap-1 text-zinc-400">
                <Clock className="w-3 h-3" />
                <span>{data.response_time}</span>
              </div>
            )}
          </div>

          {/* 元数据标签 */}
          {data.metadata && Object.keys(data.metadata).length > 0 && (
            <div className="flex items-center gap-2 flex-wrap">
              <Tag className="w-3 h-3 text-zinc-500" />
              {Object.entries(data.metadata).slice(0, 3).map(([key, value], index) => (
                <Badge
                  key={index}
                  variant="outline"
                  className="text-xs px-2 py-0 h-5 bg-[#00d4aa]/10 border-[#00d4aa]/20 text-[#00d4aa]"
                >
                  {key}: {String(value).slice(0, 20)}
                </Badge>
              ))}
            </div>
          )}

          {/* 变更详情（仅在更新操作时显示） */}
          {data.operation === 'update' && data.changes && (
            <div className="mt-3 p-3 bg-zinc-800/50 rounded-md">
              <div className="text-xs text-zinc-400 mb-2">变更详情:</div>
              <div className="space-y-1">
                {Object.entries(data.changes).map(([key, value]) => (
                  <div key={key} className="text-xs">
                    <span className="text-zinc-500">{key}:</span>
                    <span className="text-zinc-300 ml-2">
                      {typeof value === 'string' ? truncateText(value, 50) : JSON.stringify(value)}
                    </span>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default TimelineItem;
