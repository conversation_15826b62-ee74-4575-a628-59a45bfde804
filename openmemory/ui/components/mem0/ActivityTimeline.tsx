import React, { useState, useEffect, useCallback } from 'react';
import { useSelector } from 'react-redux';
import { Clock, Filter, RefreshCw, Activity } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { useToast } from '@/components/ui/use-toast';
import { realMem0Client } from '@/lib/mem0-client/realClient';
import { UIActivityItem, UIActivitiesResponse } from '@/types/mem0-api';
import { RootState } from '@/store/store';
import TimelineItem from './TimelineItem';
import CategoryFilter from './CategoryFilter';

interface ActivityTimelineProps {
  filterUserId?: string;
  filterRunId?: string;
  filterAgentId?: string;
  filterCategories?: string[];
  onFiltersChange?: (filters: {
    userId?: string;
    runId?: string;
    agentId?: string;
    categories?: string[];
  }) => void;
  className?: string;
}

const ActivityTimeline: React.FC<ActivityTimelineProps> = ({
  filterUserId,
  filterRunId,
  filterAgentId,
  filterCategories = [],
  onFiltersChange,
  className
}) => {
  const [activities, setActivities] = useState<UIActivityItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [categories, setCategories] = useState<string[]>([]);
  const [showFilters, setShowFilters] = useState(false);
  const [lastRefresh, setLastRefresh] = useState<Date>(new Date());

  const { toast } = useToast();
  const userId = useSelector((state: RootState) => state.profile.userId);

  const fetchActivities = useCallback(async () => {
    setLoading(true);
    setError(null);
    try {
      // 使用正确的UI活动API端点
      const queryParams: any = {
        limit: 50,
        offset: 0
      };
      if (filterUserId) queryParams.user_id = filterUserId;

      const activitiesResponse: UIActivitiesResponse = await realMem0Client.getUIActivities(queryParams);

      // 过滤活动（基于分类，如果需要的话）
      let filteredActivities = activitiesResponse.activities;

      if (filterCategories.length > 0) {
        // 注意：活动API可能不直接支持分类过滤，这里做客户端过滤
        filteredActivities = activitiesResponse.activities.filter(activity => {
          // 如果活动有metadata中包含categories信息
          const activityCategories = activity.metadata?.categories || [];
          return filterCategories.some(cat => activityCategories.includes(cat));
        });
      }

      setActivities(filteredActivities);
      setLastRefresh(new Date());
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch activity timeline';
      setError(errorMessage);
      console.error('Failed to fetch activities:', err);
      toast({
        title: "错误",
        description: errorMessage,
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  }, [filterUserId, filterRunId, filterAgentId, filterCategories, toast]);

  const fetchAvailableCategories = useCallback(async () => {
    try {
      const memoriesResponse = await realMem0Client.getMemories({ 
        user_id: userId, 
        limit: 1000 
      });
      const allCategories = memoriesResponse.memories
        .flatMap(memory => memory.custom_categories || [])
        .filter(Boolean);
      
      const uniqueCategories = [...new Set(allCategories)];
      setCategories(uniqueCategories);
    } catch (err) {
      console.error('Failed to fetch categories:', err);
    }
  }, []);

  const handleRefresh = useCallback(() => {
    fetchActivities();
    fetchAvailableCategories();
  }, [fetchActivities, fetchAvailableCategories]);

  const handleCategoryFilterChange = useCallback((selectedCategories: string[]) => {
    // 构建过滤器参数，过滤掉undefined值
    const filterParams: any = { categories: selectedCategories };
    if (filterUserId) filterParams.userId = filterUserId;
    if (filterRunId) filterParams.runId = filterRunId;
    if (filterAgentId) filterParams.agentId = filterAgentId;

    onFiltersChange?.(filterParams);
  }, [filterUserId, filterRunId, filterAgentId, onFiltersChange]);

  // 初始化数据
  useEffect(() => {
    fetchActivities();
    fetchAvailableCategories();
  }, [fetchActivities, fetchAvailableCategories]);

  // 自动刷新（每30秒）
  useEffect(() => {
    const interval = setInterval(() => {
      handleRefresh();
    }, 30000);

    return () => clearInterval(interval);
  }, [handleRefresh]);

  if (error) {
    return (
      <Card className={`bg-zinc-900 border-zinc-800 ${className}`}>
        <CardContent className="p-6">
          <div className="text-center text-red-400">
            <Activity className="w-8 h-8 mx-auto mb-2" />
            <p>加载活动时间线失败</p>
            <p className="text-sm text-zinc-500 mt-1">{error}</p>
            <Button 
              variant="outline" 
              size="sm" 
              onClick={handleRefresh}
              className="mt-4 border-zinc-700"
            >
              重试
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={`bg-zinc-900 border-zinc-800 ${className}`}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="text-white flex items-center gap-2">
            <Activity className="w-5 h-5 text-[#00d4aa]" />
            活动时间线
          </CardTitle>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowFilters(!showFilters)}
              className="border-zinc-700 hover:border-zinc-600"
            >
              <Filter className="w-4 h-4" />
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={handleRefresh}
              disabled={loading}
              className="border-zinc-700 hover:border-zinc-600"
            >
              <RefreshCw className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
            </Button>
          </div>
        </div>
        
        {showFilters && (
          <div className="mt-4">
            <CategoryFilter
              categories={categories}
              selectedCategories={filterCategories}
              onCategoryChange={handleCategoryFilterChange}
            />
          </div>
        )}
        
        <div className="flex items-center gap-2 text-xs text-zinc-500">
          <Clock className="w-3 h-3" />
          <span>最后更新: {lastRefresh.toLocaleTimeString()}</span>
          <span>•</span>
          <span>{activities.length} 条活动记录</span>
        </div>
      </CardHeader>
      
      <CardContent className="p-0">
        {loading ? (
          <div className="p-6">
            <div className="space-y-4">
              {[...Array(5)].map((_, i) => (
                <div key={i} className="flex items-start gap-4">
                  <div className="w-8 h-8 bg-zinc-800 rounded-full animate-pulse"></div>
                  <div className="flex-1 space-y-2">
                    <div className="h-4 bg-zinc-800 rounded animate-pulse"></div>
                    <div className="h-3 bg-zinc-800 rounded w-3/4 animate-pulse"></div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        ) : activities.length === 0 ? (
          <div className="p-6 text-center text-zinc-500">
            <Activity className="w-8 h-8 mx-auto mb-2 opacity-50" />
            <p>暂无活动记录</p>
            <p className="text-sm mt-1">当有记忆操作时，活动记录将显示在这里</p>
          </div>
        ) : (
          <div className="max-h-96 overflow-y-auto">
            <div className="space-y-0">
              {activities.map((item, index) => (
                <TimelineItem
                  key={item.id}
                  data={item}
                  isLast={index === activities.length - 1}
                />
              ))}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default ActivityTimeline;
