/**
 * Graph Memory 错误边界组件
 * 
 * 提供完善的错误处理和恢复机制：
 * - 捕获React组件错误
 * - 记录错误信息和堆栈
 * - 提供用户友好的错误界面
 * - 支持错误恢复和重试
 * - 性能监控和错误统计
 */

import React, { Component, ErrorInfo, ReactNode } from 'react';
import { AlertTriangle, RefreshCw, Bug, Home, Mail } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
  showErrorDetails?: boolean;
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
  errorId: string;
  retryCount: number;
  showDetails: boolean;
}

// 错误类型分类
enum ErrorCategory {
  RENDER_ERROR = 'render_error',
  NETWORK_ERROR = 'network_error',
  PERFORMANCE_ERROR = 'performance_error',
  DATA_ERROR = 'data_error',
  UNKNOWN_ERROR = 'unknown_error'
}

// 错误严重程度
enum ErrorSeverity {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical'
}

interface ErrorReport {
  id: string;
  timestamp: number;
  category: ErrorCategory;
  severity: ErrorSeverity;
  message: string;
  stack?: string;
  componentStack?: string;
  userAgent: string;
  url: string;
  userId?: string;
  sessionId?: string;
  retryCount: number;
}

class GraphErrorBoundary extends Component<Props, State> {
  private errorReports: ErrorReport[] = [];
  private maxRetries = 3;

  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: '',
      retryCount: 0,
      showDetails: false
    };
  }

  static getDerivedStateFromError(error: Error): Partial<State> {
    // 更新state以显示错误UI
    return {
      hasError: true,
      error,
      errorId: `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    // 记录错误信息
    this.setState({
      error,
      errorInfo
    });

    // 创建错误报告
    const errorReport = this.createErrorReport(error, errorInfo);
    this.errorReports.push(errorReport);

    // 调用外部错误处理器
    this.props.onError?.(error, errorInfo);

    // 发送错误报告到监控服务
    this.sendErrorReport(errorReport);

    // 在开发环境中打印详细错误信息
    if (process.env.NODE_ENV === 'development') {
      console.group('🚨 Graph Error Boundary Caught Error');
      console.error('Error:', error);
      console.error('Error Info:', errorInfo);
      console.error('Component Stack:', errorInfo.componentStack);
      console.groupEnd();
    }
  }

  /**
   * 创建错误报告
   */
  private createErrorReport(error: Error, errorInfo: ErrorInfo): ErrorReport {
    const category = this.categorizeError(error);
    const severity = this.assessErrorSeverity(error, category);

    return {
      id: this.state.errorId,
      timestamp: Date.now(),
      category,
      severity,
      message: error.message,
      stack: error.stack,
      componentStack: errorInfo.componentStack,
      userAgent: navigator.userAgent,
      url: window.location.href,
      userId: this.getUserId(),
      sessionId: this.getSessionId(),
      retryCount: this.state.retryCount
    };
  }

  /**
   * 错误分类
   */
  private categorizeError(error: Error): ErrorCategory {
    const message = error.message.toLowerCase();
    const stack = error.stack?.toLowerCase() || '';

    if (message.includes('network') || message.includes('fetch') || message.includes('xhr')) {
      return ErrorCategory.NETWORK_ERROR;
    }
    
    if (message.includes('performance') || message.includes('memory') || message.includes('timeout')) {
      return ErrorCategory.PERFORMANCE_ERROR;
    }
    
    if (message.includes('data') || message.includes('parse') || message.includes('json')) {
      return ErrorCategory.DATA_ERROR;
    }
    
    if (stack.includes('render') || message.includes('render')) {
      return ErrorCategory.RENDER_ERROR;
    }

    return ErrorCategory.UNKNOWN_ERROR;
  }

  /**
   * 评估错误严重程度
   */
  private assessErrorSeverity(error: Error, category: ErrorCategory): ErrorSeverity {
    // 根据错误类型和消息评估严重程度
    if (category === ErrorCategory.NETWORK_ERROR) {
      return ErrorSeverity.MEDIUM;
    }
    
    if (category === ErrorCategory.PERFORMANCE_ERROR) {
      return ErrorSeverity.HIGH;
    }
    
    if (category === ErrorCategory.DATA_ERROR) {
      return ErrorSeverity.MEDIUM;
    }
    
    if (error.message.includes('critical') || error.message.includes('fatal')) {
      return ErrorSeverity.CRITICAL;
    }

    return ErrorSeverity.LOW;
  }

  /**
   * 发送错误报告
   */
  private async sendErrorReport(report: ErrorReport): Promise<void> {
    try {
      // 在实际应用中，这里应该发送到错误监控服务
      // 例如：Sentry, LogRocket, Bugsnag等
      if (process.env.NODE_ENV === 'production') {
        // await errorMonitoringService.sendReport(report);
      }
    } catch (sendError) {
      console.warn('Failed to send error report:', sendError);
    }
  }

  /**
   * 获取用户ID
   */
  private getUserId(): string | undefined {
    // 从localStorage、Redux store或其他地方获取用户ID
    return localStorage.getItem('userId') || undefined;
  }

  /**
   * 获取会话ID
   */
  private getSessionId(): string | undefined {
    // 从sessionStorage或其他地方获取会话ID
    return sessionStorage.getItem('sessionId') || undefined;
  }

  /**
   * 重试处理
   */
  private handleRetry = () => {
    if (this.state.retryCount < this.maxRetries) {
      this.setState(prevState => ({
        hasError: false,
        error: null,
        errorInfo: null,
        retryCount: prevState.retryCount + 1,
        showDetails: false
      }));
    }
  };

  /**
   * 重置错误状态
   */
  private handleReset = () => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: '',
      retryCount: 0,
      showDetails: false
    });
  };

  /**
   * 切换详细信息显示
   */
  private toggleDetails = () => {
    this.setState(prevState => ({
      showDetails: !prevState.showDetails
    }));
  };

  /**
   * 获取错误严重程度的颜色
   */
  private getSeverityColor(severity: ErrorSeverity): string {
    switch (severity) {
      case ErrorSeverity.LOW:
        return 'bg-blue-500';
      case ErrorSeverity.MEDIUM:
        return 'bg-yellow-500';
      case ErrorSeverity.HIGH:
        return 'bg-orange-500';
      case ErrorSeverity.CRITICAL:
        return 'bg-red-500';
      default:
        return 'bg-gray-500';
    }
  }

  /**
   * 渲染错误界面
   */
  private renderErrorUI() {
    const { error, errorInfo, errorId, retryCount, showDetails } = this.state;
    const canRetry = retryCount < this.maxRetries;
    const errorReport = this.errorReports.find(report => report.id === errorId);

    return (
      <div className="flex items-center justify-center min-h-[400px] p-6">
        <Card className="w-full max-w-2xl bg-zinc-900 border-zinc-800">
          <CardHeader>
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-red-500/20 rounded-full">
                <AlertTriangle className="h-6 w-6 text-red-500" />
              </div>
              <div className="flex-1">
                <CardTitle className="text-white">
                  Graph Memory Error
                </CardTitle>
                <p className="text-zinc-400 text-sm mt-1">
                  Something went wrong while rendering the graph visualization
                </p>
              </div>
              {errorReport && (
                <Badge className={`${this.getSeverityColor(errorReport.severity)} text-white`}>
                  {errorReport.severity.toUpperCase()}
                </Badge>
              )}
            </div>
          </CardHeader>
          
          <CardContent className="space-y-4">
            {/* 错误信息 */}
            <div className="bg-zinc-800 p-4 rounded-lg">
              <h4 className="text-white font-medium mb-2">Error Message</h4>
              <p className="text-red-400 font-mono text-sm">
                {error?.message || 'Unknown error occurred'}
              </p>
            </div>

            {/* 操作按钮 */}
            <div className="flex flex-wrap gap-3">
              {canRetry && (
                <Button
                  onClick={this.handleRetry}
                  className="bg-[#00d4aa] text-black hover:bg-[#00d4aa]/90"
                >
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Retry ({this.maxRetries - retryCount} left)
                </Button>
              )}
              
              <Button
                onClick={this.handleReset}
                variant="outline"
                className="border-zinc-700 text-zinc-300 hover:bg-zinc-800"
              >
                <Home className="h-4 w-4 mr-2" />
                Reset
              </Button>
              
              <Button
                onClick={() => window.location.reload()}
                variant="outline"
                className="border-zinc-700 text-zinc-300 hover:bg-zinc-800"
              >
                <RefreshCw className="h-4 w-4 mr-2" />
                Reload Page
              </Button>
            </div>

            {/* 详细信息 */}
            {(this.props.showErrorDetails || process.env.NODE_ENV === 'development') && (
              <Collapsible>
                <CollapsibleTrigger asChild>
                  <Button
                    variant="ghost"
                    className="text-zinc-400 hover:text-white p-0"
                    onClick={this.toggleDetails}
                  >
                    <Bug className="h-4 w-4 mr-2" />
                    {showDetails ? 'Hide' : 'Show'} Technical Details
                  </Button>
                </CollapsibleTrigger>
                
                <CollapsibleContent className="mt-4">
                  <div className="space-y-3">
                    {/* 错误ID */}
                    <div>
                      <h5 className="text-zinc-300 font-medium mb-1">Error ID</h5>
                      <code className="text-xs text-zinc-400 bg-zinc-800 px-2 py-1 rounded">
                        {errorId}
                      </code>
                    </div>

                    {/* 错误堆栈 */}
                    {error?.stack && (
                      <div>
                        <h5 className="text-zinc-300 font-medium mb-1">Stack Trace</h5>
                        <pre className="text-xs text-zinc-400 bg-zinc-800 p-3 rounded overflow-x-auto max-h-40">
                          {error.stack}
                        </pre>
                      </div>
                    )}

                    {/* 组件堆栈 */}
                    {errorInfo?.componentStack && (
                      <div>
                        <h5 className="text-zinc-300 font-medium mb-1">Component Stack</h5>
                        <pre className="text-xs text-zinc-400 bg-zinc-800 p-3 rounded overflow-x-auto max-h-40">
                          {errorInfo.componentStack}
                        </pre>
                      </div>
                    )}
                  </div>
                </CollapsibleContent>
              </Collapsible>
            )}

            {/* 联系支持 */}
            <div className="border-t border-zinc-800 pt-4">
              <p className="text-zinc-400 text-sm mb-2">
                If this problem persists, please contact support with the error ID above.
              </p>
              <Button
                variant="ghost"
                size="sm"
                className="text-zinc-400 hover:text-white p-0"
                onClick={() => {
                  const subject = `Graph Memory Error Report - ${errorId}`;
                  const body = `Error ID: ${errorId}\nError Message: ${error?.message}\nURL: ${window.location.href}`;
                  window.open(`mailto:<EMAIL>?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`);
                }}
              >
                <Mail className="h-4 w-4 mr-2" />
                Contact Support
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  render() {
    if (this.state.hasError) {
      // 如果提供了自定义fallback，使用它
      if (this.props.fallback) {
        return this.props.fallback;
      }
      
      // 否则使用默认错误UI
      return this.renderErrorUI();
    }

    // 正常渲染子组件
    return this.props.children;
  }
}

export default GraphErrorBoundary;
