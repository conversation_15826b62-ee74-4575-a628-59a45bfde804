import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { Provider } from 'react-redux';
import { configureStore } from '@reduxjs/toolkit';
import { GraphMemoryFilters } from '../GraphMemoryFilters';
import graphMemoryReducer from '@/store/graphMemorySlice';

// 创建测试store
const createTestStore = (initialState = {}) => {
  return configureStore({
    reducer: {
      graphMemory: graphMemoryReducer,
    },
    preloadedState: {
      graphMemory: {
        nodes: [],
        edges: [],
        selectedNodeIds: [],
        selectedEdgeIds: [],
        selectedNode: null,
        selectedEdge: null,
        filters: {},
        viewState: {
          layout: 'force',
          zoom: 1,
          center: { x: 0, y: 0 },
          show_labels: true,
          show_edge_labels: false,
          show_minimap: true,
          show_controls: true,
          is_dragging: false,
          is_selecting: false,
          selection_mode: 'single'
        },
        stats: null,
        history: [],
        status: 'idle',
        error: null,
        batchOperation: {
          isActive: false,
          operation: null,
          result: null
        },
        ...initialState
      }
    }
  });
};

const renderWithProvider = (component: React.ReactElement, store = createTestStore()) => {
  return render(
    <Provider store={store}>
      {component}
    </Provider>
  );
};

describe('GraphMemoryFilters', () => {
  it('renders search input correctly', () => {
    renderWithProvider(<GraphMemoryFilters />);
    
    const searchInput = screen.getByPlaceholderText('Search entities and relations...');
    expect(searchInput).toBeInTheDocument();
  });

  it('renders entity types dropdown', () => {
    renderWithProvider(<GraphMemoryFilters />);
    
    const entityTypesButton = screen.getByText('Entity Types');
    expect(entityTypesButton).toBeInTheDocument();
  });

  it('renders relations dropdown', () => {
    renderWithProvider(<GraphMemoryFilters />);
    
    const relationsButton = screen.getByText('Relations');
    expect(relationsButton).toBeInTheDocument();
  });

  it('renders weight range filter', () => {
    renderWithProvider(<GraphMemoryFilters />);
    
    const weightRangeButton = screen.getByText('Weight Range');
    expect(weightRangeButton).toBeInTheDocument();
  });

  it('renders user and agent ID inputs', () => {
    renderWithProvider(<GraphMemoryFilters />);
    
    const userIdInput = screen.getByPlaceholderText('User ID');
    const agentIdInput = screen.getByPlaceholderText('Agent ID');
    
    expect(userIdInput).toBeInTheDocument();
    expect(agentIdInput).toBeInTheDocument();
  });

  it('shows clear filters button when filters are active', () => {
    const storeWithFilters = createTestStore({
      filters: {
        entity_search: 'test',
        entity_types: ['person']
      }
    });
    
    renderWithProvider(<GraphMemoryFilters />, storeWithFilters);
    
    const clearButton = screen.getByText(/Clear \(2\)/);
    expect(clearButton).toBeInTheDocument();
  });

  it('handles search input changes', () => {
    renderWithProvider(<GraphMemoryFilters />);
    
    const searchInput = screen.getByPlaceholderText('Search entities and relations...');
    fireEvent.change(searchInput, { target: { value: 'test search' } });
    
    expect(searchInput).toHaveValue('test search');
  });
});
