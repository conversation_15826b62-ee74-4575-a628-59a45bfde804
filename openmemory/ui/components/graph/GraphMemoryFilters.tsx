"use client";
import { useEffect, useState, useCallback } from "react";
import {
  Search,
  Filter,
  X,
  Calendar,
  Users,
  Network,
  Layers,
  Settings2,
  Plus,
  Minus,
  Save,
  History,
  Download,
  Edit,
  Trash2,
  Archive,
  Copy,
  Eye,
  EyeOff,
  MoreHorizontal,
  ChevronDown,
  ChevronUp,
  Zap,
  Target,
  BookOpen
} from "lucide-react";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "@/store/store";
import { updateFilters, resetFilters, clearSelection } from "@/store/graphMemorySlice";
import { GraphMemoryFilters as GraphMemoryFiltersType } from "@/types/graph-memory";
import { useGraphMemoryApi } from "@/hooks/useGraphMemoryApi";
import { useToast } from "@/hooks/use-toast";
import debounce from "lodash/debounce";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuGroup,
  DropdownMenuCheckboxItem,
} from "@/components/ui/dropdown-menu";
import { Slider } from "@/components/ui/slider";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Checkbox } from "@/components/ui/checkbox";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Separator } from "@/components/ui/separator";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";

// 高级搜索接口
interface AdvancedSearchQuery {
  semantic_search?: string;
  entity_content?: string;
  relation_content?: string;
  regex_pattern?: string;
  logic_operator?: 'AND' | 'OR';
  date_range?: {
    start?: string;
    end?: string;
  };
  connection_count_range?: {
    min?: number;
    max?: number;
  };
}

// 搜索历史接口
interface SearchHistory {
  id: string;
  query: string;
  filters: GraphMemoryFiltersType;
  timestamp: string;
  name?: string;
}

// 批量操作接口
interface BatchOperation {
  type: 'delete' | 'edit' | 'export' | 'archive' | 'hide' | 'duplicate';
  targets: 'nodes' | 'edges' | 'both';
  data?: any;
}

// 预定义的实体类型选项
const ENTITY_TYPES = [
  "person",
  "organization",
  "location",
  "event",
  "concept",
  "product",
  "document",
  "other"
];

// 预定义的关系类型选项
const RELATION_TYPES = [
  "knows",
  "works_at",
  "located_in",
  "related_to",
  "part_of",
  "created_by",
  "mentioned_in",
  "similar_to"
];

function GraphMemoryFilters() {
  const dispatch = useDispatch();
  const { toast } = useToast();
  const {
    deleteEntities,
    deleteRelations,
    updateEntity,
    updateRelation,
    exportGraphData,
    batchUpdateEntities,
    batchUpdateRelations
  } = useGraphMemoryApi();

  // Redux状态
  const filters = useSelector((state: RootState) => state.graphMemory.filters);
  const selectedNodeIds = useSelector((state: RootState) => state.graphMemory.selectedNodeIds);
  const selectedEdgeIds = useSelector((state: RootState) => state.graphMemory.selectedEdgeIds);
  const nodes = useSelector((state: RootState) => state.graphMemory.nodes);
  const edges = useSelector((state: RootState) => state.graphMemory.edges);

  // 基础搜索状态
  const [localEntitySearch, setLocalEntitySearch] = useState(filters.entity_search || "");
  const [weightRange, setWeightRange] = useState<[number, number]>([
    filters.weight_range?.min || 0,
    filters.weight_range?.max || 1
  ]);

  // 高级搜索状态
  const [showAdvancedSearch, setShowAdvancedSearch] = useState(false);
  const [advancedQuery, setAdvancedQuery] = useState<AdvancedSearchQuery>({
    logic_operator: 'AND'
  });

  // 搜索历史状态
  const [searchHistory, setSearchHistory] = useState<SearchHistory[]>([]);
  const [showSearchHistory, setShowSearchHistory] = useState(false);

  // 批量操作状态
  const [showBatchActions, setShowBatchActions] = useState(false);
  const [batchOperationInProgress, setBatchOperationInProgress] = useState(false);

  // 防抖搜索
  const debouncedEntitySearch = useCallback(
    debounce((query: string) => {
      dispatch(updateFilters({ entity_search: query || undefined }));
    }, 300),
    [dispatch]
  );

  const debouncedWeightRange = useCallback(
    debounce((range: [number, number]) => {
      dispatch(updateFilters({ 
        weight_range: { min: range[0], max: range[1] }
      }));
    }, 300),
    [dispatch]
  );

  // 处理实体搜索
  const handleEntitySearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const query = e.target.value;
    setLocalEntitySearch(query);
    debouncedEntitySearch(query);
  };

  // 处理实体类型筛选
  const handleEntityTypeToggle = (entityType: string) => {
    const currentTypes = filters.entity_types || [];
    const newTypes = currentTypes.includes(entityType)
      ? currentTypes.filter(type => type !== entityType)
      : [...currentTypes, entityType];
    
    dispatch(updateFilters({ 
      entity_types: newTypes.length > 0 ? newTypes : undefined 
    }));
  };

  // 处理关系类型筛选
  const handleRelationTypeToggle = (relationType: string) => {
    const currentTypes = filters.relation_types || [];
    const newTypes = currentTypes.includes(relationType)
      ? currentTypes.filter(type => type !== relationType)
      : [...currentTypes, relationType];
    
    dispatch(updateFilters({ 
      relation_types: newTypes.length > 0 ? newTypes : undefined 
    }));
  };

  // 处理权重范围变化
  const handleWeightRangeChange = (value: number[]) => {
    const range: [number, number] = [value[0], value[1]];
    setWeightRange(range);
    debouncedWeightRange(range);
  };

  // 处理用户ID筛选
  const handleUserIdChange = (value: string) => {
    dispatch(updateFilters({ 
      user_id: value || undefined 
    }));
  };

  // 处理代理ID筛选
  const handleAgentIdChange = (value: string) => {
    dispatch(updateFilters({ 
      agent_id: value || undefined 
    }));
  };

  // 处理活跃状态筛选
  const handleActiveStatusChange = (value: string) => {
    const isActive = value === "all" ? undefined : value === "true";
    dispatch(updateFilters({ is_active: isActive }));
  };

  // 清除所有筛选
  const handleClearFilters = () => {
    dispatch(resetFilters());
    setLocalEntitySearch("");
    setWeightRange([0, 1]);
  };

  // 计算活跃筛选数量
  const getActiveFiltersCount = () => {
    let count = 0;
    if (filters.entity_search) count++;
    if (filters.entity_types?.length) count++;
    if (filters.relation_types?.length) count++;
    if (filters.weight_range) count++;
    if (filters.user_id) count++;
    if (filters.agent_id) count++;
    if (filters.is_active !== undefined) count++;
    return count;
  };

  const activeFiltersCount = getActiveFiltersCount();

  // 高级搜索处理函数
  const handleAdvancedSearch = useCallback(async () => {
    try {
      // 构建高级搜索查询
      const searchFilters: Partial<GraphMemoryFiltersType> = { ...filters };

      if (advancedQuery.semantic_search) {
        searchFilters.entity_search = advancedQuery.semantic_search;
      }

      if (advancedQuery.date_range?.start && advancedQuery.date_range?.end) {
        searchFilters.date_range = {
          start: advancedQuery.date_range.start,
          end: advancedQuery.date_range.end
        };
      }

      // Note: connection_count_range is not part of GraphMemoryFilters interface
      // This would need to be added to the interface or handled differently

      dispatch(updateFilters(searchFilters));

      // 保存到搜索历史
      const historyItem: SearchHistory = {
        id: Date.now().toString(),
        query: advancedQuery.semantic_search || 'Advanced Search',
        filters: searchFilters,
        timestamp: new Date().toISOString()
      };

      setSearchHistory(prev => [historyItem, ...prev.slice(0, 9)]); // 保留最近10条

      toast({
        title: 'Search Applied',
        description: 'Advanced search filters have been applied',
      });
    } catch (error) {
      toast({
        title: 'Search Failed',
        description: 'Failed to apply advanced search filters',
        variant: 'destructive',
      });
    }
  }, [advancedQuery, filters, dispatch, toast]);

  // 批量操作处理函数
  const handleBatchOperation = useCallback(async (operation: BatchOperation) => {
    if (selectedNodeIds.length === 0 && selectedEdgeIds.length === 0) {
      toast({
        title: 'No Selection',
        description: 'Please select items to perform batch operations',
        variant: 'destructive',
      });
      return;
    }

    setBatchOperationInProgress(true);

    try {
      switch (operation.type) {
        case 'delete':
          if (operation.targets === 'nodes' || operation.targets === 'both') {
            await deleteEntities(selectedNodeIds);
          }
          if (operation.targets === 'edges' || operation.targets === 'both') {
            await deleteRelations(selectedEdgeIds);
          }
          dispatch(clearSelection());
          toast({
            title: 'Deleted Successfully',
            description: `Deleted ${selectedNodeIds.length} entities and ${selectedEdgeIds.length} relations`,
          });
          break;

        case 'export':
          const selectedNodes = nodes.filter(node => selectedNodeIds.includes(node.id));
          const selectedEdges = edges.filter(edge => selectedEdgeIds.includes(edge.id));
          await exportGraphData({ nodes: selectedNodes, edges: selectedEdges });
          toast({
            title: 'Export Started',
            description: 'Selected items are being exported',
          });
          break;

        case 'archive':
          // 实现归档逻辑
          toast({
            title: 'Archived Successfully',
            description: `Archived ${selectedNodeIds.length + selectedEdgeIds.length} items`,
          });
          break;

        case 'duplicate':
          // 实现复制逻辑
          toast({
            title: 'Duplicated Successfully',
            description: `Duplicated ${selectedNodeIds.length + selectedEdgeIds.length} items`,
          });
          break;

        default:
          throw new Error(`Unsupported operation: ${operation.type}`);
      }
    } catch (error) {
      toast({
        title: 'Operation Failed',
        description: `Failed to ${operation.type} selected items`,
        variant: 'destructive',
      });
    } finally {
      setBatchOperationInProgress(false);
      setShowBatchActions(false);
    }
  }, [selectedNodeIds, selectedEdgeIds, nodes, edges, deleteEntities, deleteRelations, exportGraphData, dispatch, toast]);

  // 保存搜索预设
  const handleSaveSearchPreset = useCallback((name: string) => {
    const preset: SearchHistory = {
      id: Date.now().toString(),
      query: name,
      filters: filters,
      timestamp: new Date().toISOString(),
      name: name
    };

    setSearchHistory(prev => [preset, ...prev.slice(0, 9)]);

    toast({
      title: 'Preset Saved',
      description: `Search preset "${name}" has been saved`,
    });
  }, [filters, toast]);

  // 应用搜索历史
  const handleApplySearchHistory = useCallback((historyItem: SearchHistory) => {
    dispatch(updateFilters(historyItem.filters));
    setLocalEntitySearch(historyItem.filters.entity_search || '');

    toast({
      title: 'Search Applied',
      description: `Applied search: ${historyItem.query}`,
    });
  }, [dispatch, toast]);

  useEffect(() => {
    setLocalEntitySearch(filters.entity_search || "");
  }, [filters.entity_search]);

  // 检查是否有选中的项目
  const hasSelection = selectedNodeIds.length > 0 || selectedEdgeIds.length > 0;

  return (
    <div className="space-y-4">
      {/* 主搜索和控制栏 */}
      <div className="flex flex-col md:flex-row gap-4">
        {/* 搜索框区域 */}
        <div className="flex-1 flex gap-2">
          <div className="relative flex-1">
            <Search className="absolute left-2 top-1/2 h-4 w-4 -translate-y-1/2 text-zinc-500" />
            <Input
              placeholder="Search entities and relations..."
              className="pl-8 bg-zinc-950 border-zinc-800 max-w-[500px]"
              value={localEntitySearch}
              onChange={handleEntitySearchChange}
            />
          </div>

          {/* 高级搜索按钮 */}
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowAdvancedSearch(!showAdvancedSearch)}
            className="border-zinc-700 bg-zinc-900 hover:bg-zinc-800 text-zinc-300"
          >
            <Settings2 className="h-4 w-4 mr-2" />
            Advanced
            {showAdvancedSearch ? <ChevronUp className="h-4 w-4 ml-2" /> : <ChevronDown className="h-4 w-4 ml-2" />}
          </Button>

          {/* 搜索历史按钮 */}
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowSearchHistory(!showSearchHistory)}
            className="border-zinc-700 bg-zinc-900 hover:bg-zinc-800 text-zinc-300"
          >
            <History className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* 筛选器控制区域 */}
      <div className="flex gap-2 flex-wrap">
        {/* 实体类型筛选 */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              variant="outline"
              className="h-9 px-4 border-zinc-700 bg-zinc-900 hover:bg-zinc-800"
            >
              <Layers className="h-4 w-4 mr-2" />
              Entity Types
              {filters.entity_types?.length ? (
                <Badge variant="secondary" className="ml-2 h-5 px-1.5 text-xs">
                  {filters.entity_types.length}
                </Badge>
              ) : null}
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent className="w-56 bg-zinc-900 border-zinc-800 text-zinc-100">
            <DropdownMenuLabel>Entity Types</DropdownMenuLabel>
            <DropdownMenuSeparator className="bg-zinc-800" />
            <DropdownMenuGroup>
              {ENTITY_TYPES.map((type) => (
                <DropdownMenuCheckboxItem
                  key={type}
                  checked={filters.entity_types?.includes(type) || false}
                  onCheckedChange={() => handleEntityTypeToggle(type)}
                  className="cursor-pointer"
                >
                  {type.charAt(0).toUpperCase() + type.slice(1)}
                </DropdownMenuCheckboxItem>
              ))}
            </DropdownMenuGroup>
          </DropdownMenuContent>
        </DropdownMenu>

        {/* 关系类型筛选 */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              variant="outline"
              className="h-9 px-4 border-zinc-700 bg-zinc-900 hover:bg-zinc-800"
            >
              <Network className="h-4 w-4 mr-2" />
              Relations
              {filters.relation_types?.length ? (
                <Badge variant="secondary" className="ml-2 h-5 px-1.5 text-xs">
                  {filters.relation_types.length}
                </Badge>
              ) : null}
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent className="w-56 bg-zinc-900 border-zinc-800 text-zinc-100">
            <DropdownMenuLabel>Relation Types</DropdownMenuLabel>
            <DropdownMenuSeparator className="bg-zinc-800" />
            <DropdownMenuGroup>
              {RELATION_TYPES.map((type) => (
                <DropdownMenuCheckboxItem
                  key={type}
                  checked={filters.relation_types?.includes(type) || false}
                  onCheckedChange={() => handleRelationTypeToggle(type)}
                  className="cursor-pointer"
                >
                  {type.charAt(0).toUpperCase() + type.slice(1).replace(/_/g, ' ')}
                </DropdownMenuCheckboxItem>
              ))}
            </DropdownMenuGroup>
          </DropdownMenuContent>
        </DropdownMenu>

        {/* 权重范围筛选 */}
        <Popover>
          <PopoverTrigger asChild>
            <Button
              variant="outline"
              className="h-9 px-4 border-zinc-700 bg-zinc-900 hover:bg-zinc-800"
            >
              <Filter className="h-4 w-4 mr-2" />
              Weight Range
              {filters.weight_range ? (
                <Badge variant="secondary" className="ml-2 h-5 px-1.5 text-xs">
                  {filters.weight_range.min.toFixed(1)}-{filters.weight_range.max.toFixed(1)}
                </Badge>
              ) : null}
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-80 bg-zinc-900 border-zinc-800">
            <div className="space-y-4">
              <div>
                <Label className="text-sm font-medium text-zinc-200">
                  Weight Range: {weightRange[0].toFixed(2)} - {weightRange[1].toFixed(2)}
                </Label>
              </div>
              <Slider
                value={weightRange}
                onValueChange={handleWeightRangeChange}
                max={1}
                min={0}
                step={0.01}
                className="w-full"
              />
              <div className="flex justify-between text-xs text-zinc-400">
                <span>0.00</span>
                <span>1.00</span>
              </div>
            </div>
          </PopoverContent>
        </Popover>

        {/* 用户筛选 */}
        <div className="w-32">
          <Input
            placeholder="User ID"
            className="h-9 bg-zinc-950 border-zinc-800 text-sm"
            value={filters.user_id || ""}
            onChange={(e) => handleUserIdChange(e.target.value)}
          />
        </div>

        {/* 代理筛选 */}
        <div className="w-32">
          <Input
            placeholder="Agent ID"
            className="h-9 bg-zinc-950 border-zinc-800 text-sm"
            value={filters.agent_id || ""}
            onChange={(e) => handleAgentIdChange(e.target.value)}
          />
        </div>

        {/* 活跃状态筛选 */}
        <Select
          value={filters.is_active === undefined ? "all" : String(filters.is_active)}
          onValueChange={handleActiveStatusChange}
        >
          <SelectTrigger className="w-[120px] border-zinc-700/50 bg-zinc-900 hover:bg-zinc-800">
            <SelectValue placeholder="Status" />
          </SelectTrigger>
          <SelectContent className="border-zinc-700/50 bg-zinc-900">
            <SelectItem value="all">All Status</SelectItem>
            <SelectItem value="true">Active</SelectItem>
            <SelectItem value="false">Inactive</SelectItem>
          </SelectContent>
        </Select>

        {/* 清除筛选按钮 */}
        {activeFiltersCount > 0 && (
          <Button
            variant="outline"
            className="h-9 px-4 border-zinc-700 bg-zinc-900 hover:bg-zinc-800 text-zinc-300"
            onClick={handleClearFilters}
          >
            <X className="h-4 w-4 mr-2" />
            Clear ({activeFiltersCount})
          </Button>
        )}

        {/* 批量操作按钮 */}
        {hasSelection && (
          <DropdownMenu open={showBatchActions} onOpenChange={setShowBatchActions}>
            <DropdownMenuTrigger asChild>
              <Button
                variant="outline"
                className="h-9 px-4 border-zinc-700 bg-zinc-900 hover:bg-zinc-800 text-zinc-300"
                disabled={batchOperationInProgress}
              >
                <MoreHorizontal className="h-4 w-4 mr-2" />
                Batch Actions ({selectedNodeIds.length + selectedEdgeIds.length})
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent className="w-56 bg-zinc-900 border-zinc-800 text-zinc-100">
              <DropdownMenuLabel>Batch Operations</DropdownMenuLabel>
              <DropdownMenuSeparator className="bg-zinc-800" />

              <DropdownMenuItem
                onClick={() => handleBatchOperation({ type: 'export', targets: 'both' })}
                disabled={batchOperationInProgress}
              >
                <Download className="mr-2 h-4 w-4" />
                Export Selected
              </DropdownMenuItem>

              <DropdownMenuItem
                onClick={() => handleBatchOperation({ type: 'duplicate', targets: 'both' })}
                disabled={batchOperationInProgress}
              >
                <Copy className="mr-2 h-4 w-4" />
                Duplicate Selected
              </DropdownMenuItem>

              <DropdownMenuItem
                onClick={() => handleBatchOperation({ type: 'archive', targets: 'both' })}
                disabled={batchOperationInProgress}
              >
                <Archive className="mr-2 h-4 w-4" />
                Archive Selected
              </DropdownMenuItem>

              <DropdownMenuSeparator className="bg-zinc-800" />

              <DropdownMenuItem
                onClick={() => handleBatchOperation({ type: 'delete', targets: 'both' })}
                disabled={batchOperationInProgress}
                className="text-red-400 focus:text-red-300"
              >
                <Trash2 className="mr-2 h-4 w-4" />
                Delete Selected
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        )}
      </div>

      {/* 高级搜索面板 */}
      {showAdvancedSearch && (
        <Card className="bg-zinc-900 border-zinc-800">
          <CardHeader className="pb-3">
            <CardTitle className="text-lg text-white flex items-center">
              <Zap className="h-5 w-5 mr-2 text-[#00d4aa]" />
              Advanced Search
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <Tabs defaultValue="semantic" className="w-full">
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger value="semantic">Semantic</TabsTrigger>
                <TabsTrigger value="content">Content</TabsTrigger>
                <TabsTrigger value="structure">Structure</TabsTrigger>
              </TabsList>

              <TabsContent value="semantic" className="space-y-4">
                <div>
                  <Label className="text-zinc-300">Semantic Search</Label>
                  <Textarea
                    placeholder="Describe what you're looking for in natural language..."
                    className="bg-zinc-950 border-zinc-800 text-zinc-100 mt-2"
                    value={advancedQuery.semantic_search || ''}
                    onChange={(e) => setAdvancedQuery(prev => ({ ...prev, semantic_search: e.target.value }))}
                  />
                </div>

                <div>
                  <Label className="text-zinc-300">Logic Operator</Label>
                  <RadioGroup
                    value={advancedQuery.logic_operator}
                    onValueChange={(value: 'AND' | 'OR') => setAdvancedQuery(prev => ({ ...prev, logic_operator: value }))}
                    className="flex space-x-4 mt-2"
                  >
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="AND" id="and" />
                      <Label htmlFor="and" className="text-zinc-300">AND (All conditions)</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="OR" id="or" />
                      <Label htmlFor="or" className="text-zinc-300">OR (Any condition)</Label>
                    </div>
                  </RadioGroup>
                </div>
              </TabsContent>

              <TabsContent value="content" className="space-y-4">
                <div>
                  <Label className="text-zinc-300">Entity Content</Label>
                  <Input
                    placeholder="Search in entity descriptions, names, properties..."
                    className="bg-zinc-950 border-zinc-800 text-zinc-100 mt-2"
                    value={advancedQuery.entity_content || ''}
                    onChange={(e) => setAdvancedQuery(prev => ({ ...prev, entity_content: e.target.value }))}
                  />
                </div>

                <div>
                  <Label className="text-zinc-300">Relation Content</Label>
                  <Input
                    placeholder="Search in relation descriptions, types, properties..."
                    className="bg-zinc-950 border-zinc-800 text-zinc-100 mt-2"
                    value={advancedQuery.relation_content || ''}
                    onChange={(e) => setAdvancedQuery(prev => ({ ...prev, relation_content: e.target.value }))}
                  />
                </div>

                <div>
                  <Label className="text-zinc-300">Regular Expression</Label>
                  <Input
                    placeholder="Enter regex pattern for advanced matching..."
                    className="bg-zinc-950 border-zinc-800 text-zinc-100 mt-2"
                    value={advancedQuery.regex_pattern || ''}
                    onChange={(e) => setAdvancedQuery(prev => ({ ...prev, regex_pattern: e.target.value }))}
                  />
                </div>
              </TabsContent>

              <TabsContent value="structure" className="space-y-4">
                <div>
                  <Label className="text-zinc-300">Date Range</Label>
                  <div className="grid grid-cols-2 gap-2 mt-2">
                    <Input
                      type="date"
                      placeholder="Start date"
                      className="bg-zinc-950 border-zinc-800 text-zinc-100"
                      value={advancedQuery.date_range?.start || ''}
                      onChange={(e) => setAdvancedQuery(prev => ({
                        ...prev,
                        date_range: { ...prev.date_range, start: e.target.value }
                      }))}
                    />
                    <Input
                      type="date"
                      placeholder="End date"
                      className="bg-zinc-950 border-zinc-800 text-zinc-100"
                      value={advancedQuery.date_range?.end || ''}
                      onChange={(e) => setAdvancedQuery(prev => ({
                        ...prev,
                        date_range: { ...prev.date_range, end: e.target.value }
                      }))}
                    />
                  </div>
                </div>

                <div>
                  <Label className="text-zinc-300">Connection Count Range</Label>
                  <div className="grid grid-cols-2 gap-2 mt-2">
                    <Input
                      type="number"
                      placeholder="Min connections"
                      className="bg-zinc-950 border-zinc-800 text-zinc-100"
                      value={advancedQuery.connection_count_range?.min || ''}
                      onChange={(e) => setAdvancedQuery(prev => ({
                        ...prev,
                        connection_count_range: {
                          ...prev.connection_count_range,
                          min: e.target.value ? parseInt(e.target.value) : undefined
                        }
                      }))}
                    />
                    <Input
                      type="number"
                      placeholder="Max connections"
                      className="bg-zinc-950 border-zinc-800 text-zinc-100"
                      value={advancedQuery.connection_count_range?.max || ''}
                      onChange={(e) => setAdvancedQuery(prev => ({
                        ...prev,
                        connection_count_range: {
                          ...prev.connection_count_range,
                          max: e.target.value ? parseInt(e.target.value) : undefined
                        }
                      }))}
                    />
                  </div>
                </div>
              </TabsContent>
            </Tabs>

            <div className="flex justify-between items-center pt-4 border-t border-zinc-800">
              <div className="flex space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setAdvancedQuery({ logic_operator: 'AND' })}
                  className="border-zinc-700 bg-zinc-900 hover:bg-zinc-800 text-zinc-300"
                >
                  Clear
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    const name = prompt('Enter preset name:');
                    if (name) handleSaveSearchPreset(name);
                  }}
                  className="border-zinc-700 bg-zinc-900 hover:bg-zinc-800 text-zinc-300"
                >
                  <Save className="h-4 w-4 mr-2" />
                  Save Preset
                </Button>
              </div>

              <Button
                onClick={handleAdvancedSearch}
                className="bg-[#00d4aa] text-black hover:bg-[#00d4aa]/90"
              >
                <Target className="h-4 w-4 mr-2" />
                Apply Search
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* 搜索历史面板 */}
      {showSearchHistory && (
        <Card className="bg-zinc-900 border-zinc-800">
          <CardHeader className="pb-3">
            <CardTitle className="text-lg text-white flex items-center">
              <BookOpen className="h-5 w-5 mr-2 text-[#00d4aa]" />
              Search History & Presets
            </CardTitle>
          </CardHeader>
          <CardContent>
            {searchHistory.length === 0 ? (
              <p className="text-zinc-500 text-center py-4">No search history yet</p>
            ) : (
              <div className="space-y-2">
                {searchHistory.map((item) => (
                  <div
                    key={item.id}
                    className="flex items-center justify-between p-3 bg-zinc-950 rounded-lg border border-zinc-800 hover:border-zinc-700 transition-colors"
                  >
                    <div className="flex-1">
                      <div className="font-medium text-zinc-300">
                        {item.name || item.query}
                      </div>
                      <div className="text-sm text-zinc-500">
                        {new Date(item.timestamp).toLocaleString()}
                      </div>
                    </div>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleApplySearchHistory(item)}
                      className="text-[#00d4aa] hover:text-[#00d4aa]/80"
                    >
                      Apply
                    </Button>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  );
}

export default GraphMemoryFilters;
