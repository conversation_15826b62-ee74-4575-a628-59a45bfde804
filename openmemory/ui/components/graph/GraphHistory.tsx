'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { useSelector } from 'react-redux';
import { 
  Clock, 
  Filter, 
  RefreshCw, 
  Activity, 
  Search,
  Calendar,
  User,
  GitBranch,
  Database,
  Eye,
  ChevronDown,
  ChevronUp
} from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';
import { RootState } from '@/store/store';
import { GraphMemoryHistoryItem, GraphMemoryOperationType } from '@/types/graph-memory';
import { formatDate } from '@/lib/helpers';
import GraphHistoryItem from './GraphHistoryItem';

interface GraphHistoryProps {
  filterUserId?: string;
  filterOperationType?: GraphMemoryOperationType;
  filterTargetType?: 'entity' | 'relation' | 'view';
  onFiltersChange?: (filters: {
    userId?: string;
    operationType?: GraphMemoryOperationType;
    targetType?: 'entity' | 'relation' | 'view';
  }) => void;
  className?: string;
}

// 操作类型选项
const OPERATION_TYPES: { value: GraphMemoryOperationType; label: string }[] = [
  { value: 'entity_create', label: 'Entity Created' },
  { value: 'entity_update', label: 'Entity Updated' },
  { value: 'entity_delete', label: 'Entity Deleted' },
  { value: 'relation_create', label: 'Relation Created' },
  { value: 'relation_update', label: 'Relation Updated' },
  { value: 'relation_delete', label: 'Relation Deleted' },
  { value: 'graph_layout_change', label: 'Layout Changed' },
  { value: 'filter_change', label: 'Filter Changed' },
  { value: 'view_change', label: 'View Changed' }
];

// 目标类型选项
const TARGET_TYPES = [
  { value: 'entity', label: 'Entity' },
  { value: 'relation', label: 'Relation' },
  { value: 'view', label: 'View' }
];

const GraphHistory: React.FC<GraphHistoryProps> = ({
  filterUserId,
  filterOperationType,
  filterTargetType,
  onFiltersChange,
  className = ''
}) => {
  const { toast } = useToast();
  
  // Redux state
  const history = useSelector((state: RootState) => state.graphMemory.history);
  const userId = useSelector((state: RootState) => state.profile.userId);
  
  // Local state
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedOperationType, setSelectedOperationType] = useState<string>('all');
  const [selectedTargetType, setSelectedTargetType] = useState<string>('all');
  const [showFilters, setShowFilters] = useState(false);
  const [lastRefresh, setLastRefresh] = useState<Date>(new Date());
  const [expandedItems, setExpandedItems] = useState<Set<string>>(new Set());

  // 筛选历史记录
  const filteredHistory = history.filter(item => {
    // 搜索筛选
    const matchesSearch = searchTerm === '' || 
      item.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.target_id.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.operation.toLowerCase().includes(searchTerm.toLowerCase());

    // 操作类型筛选
    const matchesOperationType = selectedOperationType === 'all' || 
      item.operation === selectedOperationType;

    // 目标类型筛选
    const matchesTargetType = selectedTargetType === 'all' || 
      item.target_type === selectedTargetType;

    // 用户筛选
    const matchesUserId = !filterUserId || item.user_id === filterUserId;

    return matchesSearch && matchesOperationType && matchesTargetType && matchesUserId;
  });

  // 按日期分组历史记录
  const groupedHistory = filteredHistory.reduce((groups, item) => {
    const date = new Date(item.timestamp).toDateString();
    if (!groups[date]) {
      groups[date] = [];
    }
    groups[date].push(item);
    return groups;
  }, {} as Record<string, GraphMemoryHistoryItem[]>);

  // 获取操作统计
  const getOperationStats = () => {
    const stats = filteredHistory.reduce((acc, item) => {
      acc[item.operation] = (acc[item.operation] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    return Object.entries(stats)
      .sort(([, a], [, b]) => b - a)
      .slice(0, 5);
  };

  // 处理筛选器变化
  const handleFiltersChange = useCallback(() => {
    if (onFiltersChange) {
      onFiltersChange({
        userId: filterUserId,
        operationType: selectedOperationType === 'all' ? undefined : selectedOperationType as GraphMemoryOperationType,
        targetType: selectedTargetType === 'all' ? undefined : selectedTargetType as 'entity' | 'relation' | 'view'
      });
    }
  }, [filterUserId, selectedOperationType, selectedTargetType, onFiltersChange]);

  // 刷新历史记录
  const handleRefresh = useCallback(() => {
    setLastRefresh(new Date());
    toast({
      title: 'Refreshed',
      description: 'History timeline has been refreshed',
    });
  }, [toast]);

  // 切换项目展开状态
  const toggleItemExpansion = (itemId: string) => {
    setExpandedItems(prev => {
      const newSet = new Set(prev);
      if (newSet.has(itemId)) {
        newSet.delete(itemId);
      } else {
        newSet.add(itemId);
      }
      return newSet;
    });
  };

  // 清空筛选器
  const clearFilters = () => {
    setSearchTerm('');
    setSelectedOperationType('all');
    setSelectedTargetType('all');
    handleFiltersChange();
  };

  const operationStats = getOperationStats();

  return (
    <div className={className}>
      <Card className="bg-zinc-900 border-zinc-800">
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="text-white flex items-center">
              <Clock className="h-5 w-5 mr-2" />
              Graph History Timeline
            </CardTitle>
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowFilters(!showFilters)}
                className="border-zinc-700 text-zinc-300 hover:bg-zinc-800"
              >
                <Filter className="h-4 w-4 mr-2" />
                Filters
                {showFilters ? <ChevronUp className="h-4 w-4 ml-2" /> : <ChevronDown className="h-4 w-4 ml-2" />}
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={handleRefresh}
                className="border-zinc-700 text-zinc-300 hover:bg-zinc-800"
              >
                <RefreshCw className="h-4 w-4 mr-2" />
                Refresh
              </Button>
            </div>
          </div>

          {/* 统计信息 */}
          <div className="flex items-center space-x-4 text-sm text-zinc-400">
            <div className="flex items-center">
              <Activity className="h-4 w-4 mr-1" />
              {filteredHistory.length} operations
            </div>
            <div className="flex items-center">
              <Calendar className="h-4 w-4 mr-1" />
              Last updated: {formatDate(lastRefresh.toISOString())}
            </div>
          </div>

          {/* 操作统计 */}
          {operationStats.length > 0 && (
            <div className="flex flex-wrap gap-2 mt-2">
              {operationStats.map(([operation, count]) => (
                <Badge key={operation} variant="secondary" className="text-xs">
                  {OPERATION_TYPES.find(t => t.value === operation)?.label || operation}: {count}
                </Badge>
              ))}
            </div>
          )}

          {/* 筛选器面板 */}
          {showFilters && (
            <div className="space-y-4 mt-4 p-4 bg-zinc-950 rounded-lg border border-zinc-800">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {/* 搜索 */}
                <div>
                  <label className="text-sm font-medium text-zinc-300 mb-2 block">
                    Search
                  </label>
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-zinc-400" />
                    <Input
                      placeholder="Search operations..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10 bg-zinc-900 border-zinc-800"
                    />
                  </div>
                </div>

                {/* 操作类型筛选 */}
                <div>
                  <label className="text-sm font-medium text-zinc-300 mb-2 block">
                    Operation Type
                  </label>
                  <Select value={selectedOperationType} onValueChange={setSelectedOperationType}>
                    <SelectTrigger className="bg-zinc-900 border-zinc-800">
                      <SelectValue placeholder="All operations" />
                    </SelectTrigger>
                    <SelectContent className="bg-zinc-900 border-zinc-800">
                      <SelectItem value="all">All Operations</SelectItem>
                      {OPERATION_TYPES.map(type => (
                        <SelectItem key={type.value} value={type.value}>
                          {type.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                {/* 目标类型筛选 */}
                <div>
                  <label className="text-sm font-medium text-zinc-300 mb-2 block">
                    Target Type
                  </label>
                  <Select value={selectedTargetType} onValueChange={setSelectedTargetType}>
                    <SelectTrigger className="bg-zinc-900 border-zinc-800">
                      <SelectValue placeholder="All targets" />
                    </SelectTrigger>
                    <SelectContent className="bg-zinc-900 border-zinc-800">
                      <SelectItem value="all">All Targets</SelectItem>
                      {TARGET_TYPES.map(type => (
                        <SelectItem key={type.value} value={type.value}>
                          {type.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="flex justify-end space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={clearFilters}
                  className="border-zinc-700 text-zinc-300 hover:bg-zinc-800"
                >
                  Clear Filters
                </Button>
                <Button
                  size="sm"
                  onClick={handleFiltersChange}
                  className="bg-[#00d4aa] text-black hover:bg-[#00d4aa]/90"
                >
                  Apply Filters
                </Button>
              </div>
            </div>
          )}
        </CardHeader>

        <CardContent>
          {/* 历史记录时间线 */}
          <div className="space-y-6">
            {Object.keys(groupedHistory).length === 0 ? (
              <div className="text-center py-8 text-zinc-500">
                <Activity className="h-12 w-12 mx-auto mb-4 text-zinc-600" />
                <p className="text-lg font-medium">No history found</p>
                <p className="text-sm">No operations match your current filters</p>
              </div>
            ) : (
              Object.entries(groupedHistory)
                .sort(([a], [b]) => new Date(b).getTime() - new Date(a).getTime())
                .map(([date, items]) => (
                  <div key={date} className="space-y-4">
                    {/* 日期分隔符 */}
                    <div className="flex items-center">
                      <div className="flex-1 h-px bg-zinc-800"></div>
                      <div className="px-4 py-2 bg-zinc-800 rounded-full text-sm text-zinc-300 font-medium">
                        {new Date(date).toLocaleDateString('en-US', {
                          weekday: 'long',
                          year: 'numeric',
                          month: 'long',
                          day: 'numeric'
                        })}
                      </div>
                      <div className="flex-1 h-px bg-zinc-800"></div>
                    </div>

                    {/* 该日期的历史项目 */}
                    <div className="space-y-3">
                      {items.map((item, index) => (
                        <GraphHistoryItem
                          key={item.id}
                          data={item}
                          isLast={index === items.length - 1}
                          isExpanded={expandedItems.has(item.id)}
                          onToggleExpansion={() => toggleItemExpansion(item.id)}
                        />
                      ))}
                    </div>
                  </div>
                ))
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default GraphHistory;
