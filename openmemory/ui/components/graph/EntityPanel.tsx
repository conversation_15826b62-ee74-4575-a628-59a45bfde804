'use client';

import React, { useState, useEffect } from 'react';
import {
  Edit,
  MoreHorizontal,
  Trash2,
  Plus,
  Search,
  Filter,
  Users,
  Tag,
  Calendar,
  Hash
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Checkbox } from '@/components/ui/checkbox';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useToast } from '@/hooks/use-toast';
import { useGraphMemoryApi } from '@/hooks/useGraphMemoryApi';
import { useSelector, useDispatch } from 'react-redux';
import { RootState } from '@/store/store';
import { formatDate } from '@/lib/helpers';
import { Mem0GraphEntity } from '@/types/graph-memory';

// 实体类型选项
const ENTITY_TYPES = [
  'person',
  'organization',
  'location',
  'event',
  'concept',
  'product',
  'document',
  'other'
];

interface EntityPanelProps {
  className?: string;
}

interface EntityFormData {
  name: string;
  type: string;
  description: string;
  properties: Record<string, any>;
}

const EntityPanel: React.FC<EntityPanelProps> = ({ className = '' }) => {
  const { toast } = useToast();
  const dispatch = useDispatch();
  
  // Redux state
  const { nodes } = useSelector((state: RootState) => state.graphMemory);
  const filters = useSelector((state: RootState) => state.graphMemory.filters);
  
  // API hooks
  const { createEntity, updateEntity, deleteEntity, fetchGraphMemories } = useGraphMemoryApi();
  
  // Local state
  const [selectedEntityIds, setSelectedEntityIds] = useState<string[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [typeFilter, setTypeFilter] = useState<string>('all');
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [editingEntity, setEditingEntity] = useState<Mem0GraphEntity | null>(null);
  const [deletingEntityId, setDeletingEntityId] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  
  // Form state
  const [formData, setFormData] = useState<EntityFormData>({
    name: '',
    type: 'person',
    description: '',
    properties: {}
  });

  // 从nodes中提取实体数据
  const entities: Mem0GraphEntity[] = nodes.map(node => ({
    id: node.id,
    name: node.data.label || node.data.name || 'Unnamed Entity',
    type: node.data.type || 'other',
    description: node.data.description || '',
    properties: node.data.properties || {},
    created_at: node.data.created_at,
    updated_at: node.data.updated_at
  }));

  // 筛选实体
  const filteredEntities = entities.filter(entity => {
    const matchesSearch = entity.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         entity.description?.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesType = typeFilter === 'all' || entity.type === typeFilter;
    return matchesSearch && matchesType;
  });

  // 选择处理
  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedEntityIds(filteredEntities.map(entity => entity.id));
    } else {
      setSelectedEntityIds([]);
    }
  };

  const handleSelectEntity = (id: string, checked: boolean) => {
    if (checked) {
      setSelectedEntityIds(prev => [...prev, id]);
    } else {
      setSelectedEntityIds(prev => prev.filter(entityId => entityId !== id));
    }
  };

  const isAllSelected = filteredEntities.length > 0 && 
                       selectedEntityIds.length === filteredEntities.length;
  const isPartiallySelected = selectedEntityIds.length > 0 && 
                             selectedEntityIds.length < filteredEntities.length;

  // 表单处理
  const resetForm = () => {
    setFormData({
      name: '',
      type: 'person',
      description: '',
      properties: {}
    });
  };

  const handleCreateEntity = async () => {
    if (!formData.name.trim()) {
      toast({
        title: 'Error',
        description: 'Entity name is required',
        variant: 'destructive',
      });
      return;
    }

    setIsLoading(true);
    try {
      await createEntity({
        name: formData.name,
        type: formData.type,
        description: formData.description,
        properties: formData.properties
      });

      toast({
        title: 'Success',
        description: 'Entity created successfully',
      });

      setIsCreateDialogOpen(false);
      resetForm();
      
      // 刷新数据
      await fetchGraphMemories(filters);
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to create entity',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleEditEntity = (entity: Mem0GraphEntity) => {
    setEditingEntity(entity);
    setFormData({
      name: entity.name,
      type: entity.type,
      description: entity.description || '',
      properties: entity.properties || {}
    });
    setIsEditDialogOpen(true);
  };

  const handleUpdateEntity = async () => {
    if (!editingEntity || !formData.name.trim()) {
      toast({
        title: 'Error',
        description: 'Entity name is required',
        variant: 'destructive',
      });
      return;
    }

    setIsLoading(true);
    try {
      await updateEntity(editingEntity.id, {
        name: formData.name,
        type: formData.type,
        description: formData.description,
        properties: formData.properties
      });

      toast({
        title: 'Success',
        description: 'Entity updated successfully',
      });

      setIsEditDialogOpen(false);
      setEditingEntity(null);
      resetForm();
      
      // 刷新数据
      await fetchGraphMemories(filters);
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to update entity',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleDeleteEntity = async (entityId: string) => {
    setDeletingEntityId(entityId);
    setIsDeleteDialogOpen(true);
  };

  const confirmDeleteEntity = async () => {
    if (!deletingEntityId) return;

    setIsLoading(true);
    try {
      await deleteEntity(deletingEntityId);

      toast({
        title: 'Success',
        description: 'Entity deleted successfully',
      });

      setIsDeleteDialogOpen(false);
      setDeletingEntityId(null);
      
      // 刷新数据
      await fetchGraphMemories(filters);
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to delete entity',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleBatchDelete = async () => {
    if (selectedEntityIds.length === 0) return;

    setIsLoading(true);
    try {
      await Promise.all(selectedEntityIds.map(id => deleteEntity(id)));

      toast({
        title: 'Success',
        description: `${selectedEntityIds.length} entities deleted successfully`,
      });

      setSelectedEntityIds([]);
      
      // 刷新数据
      await fetchGraphMemories(filters);
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to delete entities',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className={className}>
      <Card className="bg-zinc-900 border-zinc-800">
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="text-white flex items-center">
              <Users className="h-5 w-5 mr-2" />
              Entity Management
            </CardTitle>
            <div className="flex items-center space-x-2">
              {selectedEntityIds.length > 0 && (
                <Button
                  variant="destructive"
                  size="sm"
                  onClick={handleBatchDelete}
                  disabled={isLoading}
                >
                  <Trash2 className="h-4 w-4 mr-2" />
                  Delete Selected ({selectedEntityIds.length})
                </Button>
              )}
              <Button
                onClick={() => setIsCreateDialogOpen(true)}
                className="bg-[#00d4aa] text-black hover:bg-[#00d4aa]/90"
                size="sm"
              >
                <Plus className="h-4 w-4 mr-2" />
                Add Entity
              </Button>
            </div>
          </div>
          
          {/* 搜索和筛选 */}
          <div className="flex items-center space-x-4 mt-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-zinc-400" />
              <Input
                placeholder="Search entities..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 bg-zinc-950 border-zinc-800"
              />
            </div>
            <Select value={typeFilter} onValueChange={setTypeFilter}>
              <SelectTrigger className="w-[180px] border-zinc-700/50 bg-zinc-900">
                <Filter className="h-4 w-4 mr-2" />
                <SelectValue placeholder="Filter by type" />
              </SelectTrigger>
              <SelectContent className="border-zinc-700/50 bg-zinc-900">
                <SelectItem value="all">All Types</SelectItem>
                {ENTITY_TYPES.map(type => (
                  <SelectItem key={type} value={type}>
                    {type.charAt(0).toUpperCase() + type.slice(1)}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </CardHeader>
        
        <CardContent>
          <div className="rounded-md border border-zinc-800">
            <Table>
              <TableHeader>
                <TableRow className="bg-zinc-800 hover:bg-zinc-800">
                  <TableHead className="w-[50px] pl-4">
                    <Checkbox
                      className="data-[state=checked]:border-primary border-zinc-500/50"
                      checked={isAllSelected}
                      data-state={
                        isPartiallySelected
                          ? "indeterminate"
                          : isAllSelected
                          ? "checked"
                          : "unchecked"
                      }
                      onCheckedChange={handleSelectAll}
                    />
                  </TableHead>
                  <TableHead className="border-zinc-700">
                    <div className="flex items-center">
                      <Hash className="mr-1 h-4 w-4" />
                      Name
                    </div>
                  </TableHead>
                  <TableHead className="border-zinc-700">
                    <div className="flex items-center">
                      <Tag className="mr-1 h-4 w-4" />
                      Type
                    </div>
                  </TableHead>
                  <TableHead className="border-zinc-700">Description</TableHead>
                  <TableHead className="w-[140px] border-zinc-700">
                    <div className="flex items-center justify-center">
                      <Calendar className="mr-1 h-4 w-4" />
                      Created
                    </div>
                  </TableHead>
                  <TableHead className="text-right border-zinc-700">
                    <div className="flex items-center justify-end">
                      <MoreHorizontal className="h-4 w-4 mr-2" />
                    </div>
                  </TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredEntities.map((entity) => (
                  <TableRow
                    key={entity.id}
                    className={`hover:bg-zinc-900/50 ${isLoading ? "animate-pulse opacity-50" : ""}`}
                  >
                    <TableCell className="pl-4">
                      <Checkbox
                        className="data-[state=checked]:border-primary border-zinc-500/50"
                        checked={selectedEntityIds.includes(entity.id)}
                        onCheckedChange={(checked) =>
                          handleSelectEntity(entity.id, checked as boolean)
                        }
                      />
                    </TableCell>
                    <TableCell className="font-medium text-white">
                      {entity.name}
                    </TableCell>
                    <TableCell>
                      <Badge variant="secondary" className="capitalize">
                        {entity.type}
                      </Badge>
                    </TableCell>
                    <TableCell className="text-zinc-300 max-w-[300px] truncate">
                      {entity.description || 'No description'}
                    </TableCell>
                    <TableCell className="text-center text-zinc-400">
                      {entity.created_at ? formatDate(entity.created_at) : 'Unknown'}
                    </TableCell>
                    <TableCell className="text-right">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="icon" className="h-8 w-8">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent
                          align="end"
                          className="bg-zinc-900 border-zinc-800"
                        >
                          <DropdownMenuItem
                            className="cursor-pointer"
                            onClick={() => handleEditEntity(entity)}
                          >
                            <Edit className="h-4 w-4 mr-2" />
                            Edit
                          </DropdownMenuItem>
                          <DropdownMenuSeparator className="bg-zinc-800" />
                          <DropdownMenuItem
                            className="cursor-pointer text-red-400 hover:text-red-300"
                            onClick={() => handleDeleteEntity(entity.id)}
                          >
                            <Trash2 className="h-4 w-4 mr-2" />
                            Delete
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))}
                {filteredEntities.length === 0 && (
                  <TableRow>
                    <TableCell colSpan={6} className="text-center py-8 text-zinc-500">
                      {entities.length === 0 ? 'No entities found' : 'No entities match your filters'}
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>

      {/* Create Entity Dialog */}
      <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
        <DialogContent className="bg-zinc-900 border-zinc-800 text-white">
          <DialogHeader>
            <DialogTitle>Create New Entity</DialogTitle>
            <DialogDescription className="text-zinc-400">
              Add a new entity to the graph memory.
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-4">
            <div>
              <Label htmlFor="name">Name *</Label>
              <Input
                id="name"
                value={formData.name}
                onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                className="bg-zinc-950 border-zinc-800"
                placeholder="Enter entity name"
              />
            </div>
            
            <div>
              <Label htmlFor="type">Type</Label>
              <Select
                value={formData.type}
                onValueChange={(value) => setFormData(prev => ({ ...prev, type: value }))}
              >
                <SelectTrigger className="bg-zinc-950 border-zinc-800">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent className="bg-zinc-900 border-zinc-800">
                  {ENTITY_TYPES.map(type => (
                    <SelectItem key={type} value={type}>
                      {type.charAt(0).toUpperCase() + type.slice(1)}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            <div>
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                value={formData.description}
                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                className="bg-zinc-950 border-zinc-800"
                placeholder="Enter entity description"
                rows={3}
              />
            </div>
          </div>
          
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsCreateDialogOpen(false)}
              className="border-zinc-700 text-zinc-300 hover:bg-zinc-800"
            >
              Cancel
            </Button>
            <Button
              onClick={handleCreateEntity}
              disabled={isLoading}
              className="bg-[#00d4aa] text-black hover:bg-[#00d4aa]/90"
            >
              {isLoading ? 'Creating...' : 'Create Entity'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Edit Entity Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="bg-zinc-900 border-zinc-800 text-white">
          <DialogHeader>
            <DialogTitle>Edit Entity</DialogTitle>
            <DialogDescription className="text-zinc-400">
              Update entity information.
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-4">
            <div>
              <Label htmlFor="edit-name">Name *</Label>
              <Input
                id="edit-name"
                value={formData.name}
                onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                className="bg-zinc-950 border-zinc-800"
                placeholder="Enter entity name"
              />
            </div>
            
            <div>
              <Label htmlFor="edit-type">Type</Label>
              <Select
                value={formData.type}
                onValueChange={(value) => setFormData(prev => ({ ...prev, type: value }))}
              >
                <SelectTrigger className="bg-zinc-950 border-zinc-800">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent className="bg-zinc-900 border-zinc-800">
                  {ENTITY_TYPES.map(type => (
                    <SelectItem key={type} value={type}>
                      {type.charAt(0).toUpperCase() + type.slice(1)}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            <div>
              <Label htmlFor="edit-description">Description</Label>
              <Textarea
                id="edit-description"
                value={formData.description}
                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                className="bg-zinc-950 border-zinc-800"
                placeholder="Enter entity description"
                rows={3}
              />
            </div>
          </div>
          
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsEditDialogOpen(false)}
              className="border-zinc-700 text-zinc-300 hover:bg-zinc-800"
            >
              Cancel
            </Button>
            <Button
              onClick={handleUpdateEntity}
              disabled={isLoading}
              className="bg-[#00d4aa] text-black hover:bg-[#00d4aa]/90"
            >
              {isLoading ? 'Updating...' : 'Update Entity'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent className="bg-zinc-900 border-zinc-800 text-white">
          <DialogHeader>
            <DialogTitle>Delete Entity</DialogTitle>
            <DialogDescription className="text-zinc-400">
              Are you sure you want to delete this entity? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsDeleteDialogOpen(false)}
              className="border-zinc-700 text-zinc-300 hover:bg-zinc-800"
            >
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={confirmDeleteEntity}
              disabled={isLoading}
            >
              {isLoading ? 'Deleting...' : 'Delete'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default EntityPanel;
