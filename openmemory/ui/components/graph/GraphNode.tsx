'use client';

import React, { memo, useMemo } from 'react';
import { Handle, Position, NodeProps } from 'reactflow';
import { Badge } from '@/components/ui/badge';
import { 
  User, 
  Building, 
  MapPin, 
  Calendar, 
  Tag, 
  FileText, 
  Globe,
  Zap,
  Database,
  Settings
} from 'lucide-react';

// 节点数据接口
interface GraphNodeData {
  id: string;
  label: string;
  type: string;
  description?: string;
  properties?: Record<string, any>;
  metadata?: Record<string, any>;
}

// 实体类型图标映射
const getEntityIcon = (type: string) => {
  const iconMap: Record<string, React.ComponentType<any>> = {
    person: User,
    organization: Building,
    location: MapPin,
    event: Calendar,
    concept: Tag,
    document: FileText,
    website: Globe,
    skill: Zap,
    data: Database,
    other: Settings
  };
  
  return iconMap[type.toLowerCase()] || iconMap.other;
};

// 实体类型颜色映射
const getEntityColor = (type: string) => {
  const colorMap: Record<string, { bg: string; border: string; text: string; icon: string }> = {
    person: {
      bg: 'bg-blue-500/10',
      border: 'border-blue-500/30',
      text: 'text-blue-400',
      icon: 'text-blue-400'
    },
    organization: {
      bg: 'bg-purple-500/10',
      border: 'border-purple-500/30',
      text: 'text-purple-400',
      icon: 'text-purple-400'
    },
    location: {
      bg: 'bg-green-500/10',
      border: 'border-green-500/30',
      text: 'text-green-400',
      icon: 'text-green-400'
    },
    event: {
      bg: 'bg-yellow-500/10',
      border: 'border-yellow-500/30',
      text: 'text-yellow-400',
      icon: 'text-yellow-400'
    },
    concept: {
      bg: 'bg-pink-500/10',
      border: 'border-pink-500/30',
      text: 'text-pink-400',
      icon: 'text-pink-400'
    },
    document: {
      bg: 'bg-orange-500/10',
      border: 'border-orange-500/30',
      text: 'text-orange-400',
      icon: 'text-orange-400'
    },
    website: {
      bg: 'bg-cyan-500/10',
      border: 'border-cyan-500/30',
      text: 'text-cyan-400',
      icon: 'text-cyan-400'
    },
    skill: {
      bg: 'bg-red-500/10',
      border: 'border-red-500/30',
      text: 'text-red-400',
      icon: 'text-red-400'
    },
    data: {
      bg: 'bg-indigo-500/10',
      border: 'border-indigo-500/30',
      text: 'text-indigo-400',
      icon: 'text-indigo-400'
    },
    other: {
      bg: 'bg-zinc-500/10',
      border: 'border-zinc-500/30',
      text: 'text-zinc-400',
      icon: 'text-zinc-400'
    }
  };
  
  return colorMap[type.toLowerCase()] || colorMap.other;
};

const GraphNode: React.FC<NodeProps<GraphNodeData>> = ({ 
  data, 
  selected, 
  dragging 
}) => {
  const Icon = useMemo(() => getEntityIcon(data.type), [data.type]);
  const colors = useMemo(() => getEntityColor(data.type), [data.type]);

  // 计算节点样式
  const nodeStyle = useMemo(() => {
    let baseClasses = `
      relative min-w-[160px] max-w-[240px] p-3 rounded-lg border-2 
      bg-zinc-900/95 backdrop-blur-sm transition-all duration-200
      hover:shadow-lg hover:shadow-[#00d4aa]/20
    `;

    if (selected) {
      baseClasses += ` ${colors.border} shadow-lg shadow-[#00d4aa]/30 ring-2 ring-[#00d4aa]/50`;
    } else {
      baseClasses += ` border-zinc-700 hover:${colors.border}`;
    }

    if (dragging) {
      baseClasses += ' shadow-xl scale-105';
    }

    return baseClasses;
  }, [selected, dragging, colors]);

  // 处理长文本截断
  const truncateText = (text: string, maxLength: number = 50) => {
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength) + '...';
  };

  // 获取关键属性
  const getKeyProperties = () => {
    if (!data.properties) return [];
    
    const keyProps = Object.entries(data.properties)
      .filter(([key, value]) => value && key !== 'id' && key !== 'name')
      .slice(0, 3); // 最多显示3个属性
    
    return keyProps;
  };

  const keyProperties = getKeyProperties();

  return (
    <div className={nodeStyle}>
      {/* 连接点 */}
      <Handle
        type="target"
        position={Position.Top}
        className="w-3 h-3 bg-[#00d4aa] border-2 border-zinc-900"
        style={{ top: -6 }}
      />
      <Handle
        type="source"
        position={Position.Bottom}
        className="w-3 h-3 bg-[#00d4aa] border-2 border-zinc-900"
        style={{ bottom: -6 }}
      />
      <Handle
        type="target"
        position={Position.Left}
        className="w-3 h-3 bg-[#00d4aa] border-2 border-zinc-900"
        style={{ left: -6 }}
      />
      <Handle
        type="source"
        position={Position.Right}
        className="w-3 h-3 bg-[#00d4aa] border-2 border-zinc-900"
        style={{ right: -6 }}
      />

      {/* 节点内容 */}
      <div className="space-y-2">
        {/* 头部：图标和类型 */}
        <div className="flex items-center justify-between">
          <div className={`p-1.5 rounded-md ${colors.bg}`}>
            <Icon className={`w-4 h-4 ${colors.icon}`} />
          </div>
          <Badge 
            variant="secondary" 
            className={`text-xs ${colors.bg} ${colors.text} border-transparent`}
          >
            {data.type}
          </Badge>
        </div>

        {/* 主标签 */}
        <div>
          <h3 className="text-sm font-semibold text-white leading-tight">
            {truncateText(data.label, 30)}
          </h3>
          {data.description && (
            <p className="text-xs text-zinc-400 mt-1 leading-tight">
              {truncateText(data.description, 60)}
            </p>
          )}
        </div>

        {/* 关键属性 */}
        {keyProperties.length > 0 && (
          <div className="space-y-1">
            {keyProperties.map(([key, value]) => (
              <div key={key} className="flex items-center justify-between text-xs">
                <span className="text-zinc-500 capitalize">
                  {key.replace(/_/g, ' ')}:
                </span>
                <span className="text-zinc-300 ml-2 truncate max-w-[100px]">
                  {String(value)}
                </span>
              </div>
            ))}
          </div>
        )}

        {/* 选中状态指示器 */}
        {selected && (
          <div className="absolute -top-1 -right-1 w-3 h-3 bg-[#00d4aa] rounded-full border-2 border-zinc-900 animate-pulse" />
        )}
      </div>
    </div>
  );
};

export default memo(GraphNode);
