# Graph Visualization Components

Graph Memory可视化组件库，基于React Flow构建，用于展示和交互Mem0图记忆数据。

## 组件概览

### GraphVisualization
主要的图可视化组件，提供完整的图渲染和交互功能。

**特性：**
- 🎨 自定义节点和边渲染
- 🔄 多种布局算法（力导向、层次、圆形、网格）
- 🎯 交互功能（选择、拖拽、缩放、平移）
- 📊 实时统计信息显示
- 🖥️ 全屏模式支持
- 🗺️ 小地图导航
- 🎛️ 完整的控制面板

### GraphNode
自定义节点组件，支持不同实体类型的可视化。

**支持的实体类型：**
- `person` - 人员（蓝色）
- `organization` - 组织（紫色）
- `location` - 位置（绿色）
- `event` - 事件（黄色）
- `concept` - 概念（粉色）
- `document` - 文档（橙色）
- `website` - 网站（青色）
- `skill` - 技能（红色）
- `data` - 数据（靛蓝色）
- `other` - 其他（灰色）

### GraphEdge
自定义边组件，支持不同关系类型的可视化。

**支持的关系类型：**
- `knows` - 认识关系（蓝色）
- `works_at` - 工作关系（紫色）
- `lives_in` - 居住关系（绿色）
- `related_to` - 相关关系（黄色）
- `part_of` - 从属关系（红色）
- `created_by` - 创建关系（青色）
- `depends_on` - 依赖关系（粉色）
- `similar_to` - 相似关系（靛蓝色）

## 使用方法

### 基本使用

```tsx
import GraphVisualization from '@/components/graph/GraphVisualization';

function MyComponent() {
  const handleNodeClick = (node) => {
    console.log('Node clicked:', node);
  };

  const handleEdgeClick = (edge) => {
    console.log('Edge clicked:', edge);
  };

  return (
    <GraphVisualization
      height="600px"
      onNodeClick={handleNodeClick}
      onEdgeClick={handleEdgeClick}
      className="border border-zinc-700"
    />
  );
}
```

### 与Redux集成

组件自动从Redux store中读取图数据：

```tsx
// 在Redux slice中设置数据
dispatch(setNodes(nodes));
dispatch(setEdges(edges));

// 组件会自动渲染更新的数据
```

### 数据格式

**节点数据格式：**
```typescript
interface GraphNodeData {
  id: string;
  label: string;
  type: string;
  description?: string;
  properties?: Record<string, any>;
  metadata?: Record<string, any>;
}
```

**边数据格式：**
```typescript
interface GraphEdgeData {
  id: string;
  label?: string;
  relation_type: string;
  weight?: number;
  description?: string;
  properties?: Record<string, any>;
  metadata?: Record<string, any>;
}
```

## 交互功能

### 基本操作
- **选择**：点击节点或边进行选择
- **拖拽**：拖拽节点改变位置
- **缩放**：鼠标滚轮缩放画布
- **平移**：拖拽空白区域平移画布

### 工具栏功能
- **布局切换**：力导向、层次、圆形、网格布局
- **视图控制**：放大、缩小、适应视图、重置
- **全屏模式**：最大化可视化区域

### 控制面板
- **缩放控制**：精确的缩放操作
- **适应视图**：自动调整视图以显示所有节点
- **小地图**：提供画布导航概览

## 样式定制

### 主题色彩
组件使用Mem0品牌色彩：
- 主色调：`#00d4aa`（青绿色）
- 背景：`zinc-950`（深黑色）
- 边框：`zinc-800`（深灰色）

### 自定义样式
可以通过className属性添加自定义样式：

```tsx
<GraphVisualization
  className="custom-graph border-2 border-blue-500"
  height="800px"
/>
```

## 性能优化

### 大数据集处理
- 使用React.memo优化节点和边组件
- 支持虚拟化渲染（计划中）
- 智能LOD（细节层次）系统（计划中）

### 渲染优化
- 使用useMemo缓存计算结果
- 避免不必要的重新渲染
- 优化事件处理器

## 测试

运行测试页面：
```bash
npm run dev
# 访问 http://localhost:3000/test-graph
```

## 依赖

- `reactflow` - 图可视化核心库
- `@reactflow/core` - React Flow核心功能
- `@reactflow/controls` - 控制组件
- `@reactflow/minimap` - 小地图组件
- `lucide-react` - 图标库
- `@reduxjs/toolkit` - 状态管理

## 开发指南

### 添加新的节点类型
1. 在`getEntityIcon`中添加图标映射
2. 在`getEntityColor`中添加颜色配置
3. 更新TypeScript类型定义

### 添加新的边类型
1. 在`getRelationColor`中添加颜色映射
2. 在`formatRelationType`中添加显示格式
3. 更新相关文档

### 性能调优
- 监控组件渲染次数
- 优化大数据集的处理
- 使用React DevTools分析性能
