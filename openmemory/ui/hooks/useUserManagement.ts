import { useState, useCallback, useEffect } from 'react';
import { realMem0Client } from '@/lib/mem0-client';

export interface UserInfo {
  id: string;
  name?: string;
  memory_count: number;
  last_active?: string;
  created_at?: string;
}

interface UseUserManagementReturn {
  users: UserInfo[];
  currentUserId: string | null;
  isLoading: boolean;
  error: string | null;
  setCurrentUserId: (userId: string) => void;
  fetchUsers: () => Promise<void>;
  getUserStats: (userId: string) => Promise<any>;
  createUser: (userId: string, name?: string) => Promise<void>;
  deleteUser: (userId: string) => Promise<void>;
}

export const useUserManagement = (): UseUserManagementReturn => {
  const [users, setUsers] = useState<UserInfo[]>([]);
  const [currentUserId, setCurrentUserIdState] = useState<string | null>(
    typeof window !== 'undefined' ? localStorage.getItem('mem0_current_user_id') : null
  );
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const setCurrentUserId = useCallback((userId: string) => {
    setCurrentUserIdState(userId);
    if (typeof window !== 'undefined') {
      localStorage.setItem('mem0_current_user_id', userId);
    }
  }, []);

  const fetchUsers = useCallback(async () => {
    setIsLoading(true);
    setError(null);
    try {
      // 由于Mem0 API没有直接的用户列表端点，我们通过统计数据来推断用户
      // 这是一个简化实现，实际应用中可能需要维护用户列表
      const stats = await realMem0Client.getStats();
      
      // 模拟用户数据，实际应用中应该从专门的用户管理端点获取
      const mockUsers: UserInfo[] = [
        {
          id: 'default',
          name: '默认用户',
          memory_count: stats.total_memories || 0,
          last_active: new Date().toISOString(),
          created_at: new Date().toISOString()
        }
      ];

      // 如果有当前用户ID且不在列表中，添加它
      if (currentUserId && !mockUsers.find(u => u.id === currentUserId)) {
        const userStats = await getUserStats(currentUserId);
        mockUsers.push({
          id: currentUserId,
          name: `用户 ${currentUserId}`,
          memory_count: userStats.total_memories || 0,
          last_active: new Date().toISOString(),
          created_at: new Date().toISOString()
        });
      }

      setUsers(mockUsers);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch users';
      setError(errorMessage);
      console.error('Failed to fetch users:', err);
    } finally {
      setIsLoading(false);
    }
  }, [currentUserId]);

  const getUserStats = useCallback(async (userId: string) => {
    try {
      return await realMem0Client.getStats(userId);
    } catch (err) {
      console.error(`Failed to get stats for user ${userId}:`, err);
      return { total_memories: 0 };
    }
  }, []);

  const createUser = useCallback(async (userId: string, name?: string) => {
    setIsLoading(true);
    setError(null);
    try {
      // 由于Mem0 API没有专门的用户创建端点，我们通过创建一个初始记忆来"创建"用户
      await realMem0Client.createMemory({
        messages: [{ role: 'system', content: `User ${userId} initialized` }],
        user_id: userId,
        metadata: { user_name: name, is_initialization: true }
      });

      // 更新用户列表
      await fetchUsers();
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to create user';
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setIsLoading(false);
    }
  }, [fetchUsers]);

  const deleteUser = useCallback(async (userId: string) => {
    setIsLoading(true);
    setError(null);
    try {
      // 删除用户的所有记忆
      await realMem0Client.deleteAllMemories({ user_id: userId });
      
      // 如果删除的是当前用户，清除当前用户ID
      if (currentUserId === userId) {
        setCurrentUserId('');
      }

      // 更新用户列表
      await fetchUsers();
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to delete user';
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setIsLoading(false);
    }
  }, [currentUserId, setCurrentUserId, fetchUsers]);

  // 初始化时获取用户列表
  useEffect(() => {
    fetchUsers();
  }, [fetchUsers]);

  return {
    users,
    currentUserId,
    isLoading,
    error,
    setCurrentUserId,
    fetchUsers,
    getUserStats,
    createUser,
    deleteUser
  };
};
