import { useState, useCallback, useEffect } from 'react';
import { realMem0Client } from '@/lib/mem0-client';

export interface AppInfo {
  id: string;
  name: string;
  memory_count: number;
  last_active?: string;
  created_at?: string;
  description?: string;
}

interface UseAppManagementReturn {
  apps: AppInfo[];
  currentRunId: string | null;
  isLoading: boolean;
  error: string | null;
  setCurrentRunId: (runId: string) => void;
  fetchApps: () => Promise<void>;
  getAppStats: (runId: string) => Promise<any>;
  createApp: (runId: string, name?: string, description?: string) => Promise<void>;
  deleteApp: (runId: string) => Promise<void>;
}

export const useAppManagement = (): UseAppManagementReturn => {
  const [apps, setApps] = useState<AppInfo[]>([]);
  const [currentRunId, setCurrentRunIdState] = useState<string | null>(
    typeof window !== 'undefined' ? localStorage.getItem('mem0_current_run_id') : null
  );
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const setCurrentRunId = useCallback((runId: string) => {
    setCurrentRunIdState(runId);
    if (typeof window !== 'undefined') {
      localStorage.setItem('mem0_current_run_id', runId);
    }
  }, []);

  const fetchApps = useCallback(async () => {
    setIsLoading(true);
    setError(null);
    try {
      // 由于Mem0 API没有直接的APP列表端点，我们通过获取记忆来推断APP
      const memories = await realMem0Client.getMemories({ 
        user_id: userId, 
        limit: 1000 
      });
      
      // 从记忆中提取唯一的run_id
      const runIdMap = new Map<string, AppInfo>();
      
      memories.memories.forEach(memory => {
        const runId = memory.run_id || 'default';
        if (!runIdMap.has(runId)) {
          runIdMap.set(runId, {
            id: runId,
            name: runId === 'default' ? '默认应用' : `应用 ${runId}`,
            memory_count: 0,
            last_active: memory.updated_at || memory.created_at || new Date().toISOString(),
            created_at: memory.created_at || new Date().toISOString(),
            description: runId === 'default' ? '默认应用实例' : `运行实例 ${runId}`
          });
        }
        
        const app = runIdMap.get(runId)!;
        app.memory_count++;
        
        // 更新最后活跃时间
        const memoryTime = memory.updated_at || memory.created_at;
        if (memoryTime && (!app.last_active || memoryTime > app.last_active)) {
          app.last_active = memoryTime;
        }
      });

      // 如果没有找到任何APP，添加默认APP
      if (runIdMap.size === 0) {
        runIdMap.set('default', {
          id: 'default',
          name: '默认应用',
          memory_count: 0,
          last_active: new Date().toISOString(),
          created_at: new Date().toISOString(),
          description: '默认应用实例'
        });
      }

      // 如果有当前runId且不在列表中，添加它
      if (currentRunId && !runIdMap.has(currentRunId)) {
        const appStats = await getAppStats(currentRunId);
        runIdMap.set(currentRunId, {
          id: currentRunId,
          name: `应用 ${currentRunId}`,
          memory_count: appStats.total_memories || 0,
          last_active: new Date().toISOString(),
          created_at: new Date().toISOString(),
          description: `运行实例 ${currentRunId}`
        });
      }

      const appsList = Array.from(runIdMap.values()).sort((a, b) => {
        // 默认应用排在前面
        if (a.id === 'default') return -1;
        if (b.id === 'default') return 1;
        // 按最后活跃时间排序
        return (b.last_active || '').localeCompare(a.last_active || '');
      });

      setApps(appsList);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch apps';
      setError(errorMessage);
      console.error('Failed to fetch apps:', err);
    } finally {
      setIsLoading(false);
    }
  }, [currentRunId]);

  const getAppStats = useCallback(async (runId: string) => {
    try {
      // 获取特定run_id的记忆统计
      const memories = await realMem0Client.getMemories({ run_id: runId, limit: 1 });
      return { total_memories: memories.total };
    } catch (err) {
      console.error(`Failed to get stats for app ${runId}:`, err);
      return { total_memories: 0 };
    }
  }, []);

  const createApp = useCallback(async (runId: string, name?: string, description?: string) => {
    setIsLoading(true);
    setError(null);
    try {
      // 通过创建一个初始记忆来"创建"APP
      await realMem0Client.createMemory({
        messages: [{ role: 'system', content: `App ${runId} initialized` }],
        run_id: runId,
        metadata: { 
          app_name: name || `应用 ${runId}`, 
          description: description,
          is_initialization: true 
        }
      });

      // 更新APP列表
      await fetchApps();
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to create app';
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setIsLoading(false);
    }
  }, [fetchApps]);

  const deleteApp = useCallback(async (runId: string) => {
    setIsLoading(true);
    setError(null);
    try {
      // 删除APP的所有记忆
      await realMem0Client.deleteAllMemories({ run_id: runId });
      
      // 如果删除的是当前APP，清除当前runId
      if (currentRunId === runId) {
        setCurrentRunId('');
      }

      // 更新APP列表
      await fetchApps();
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to delete app';
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setIsLoading(false);
    }
  }, [currentRunId, setCurrentRunId, fetchApps]);

  // 初始化时获取APP列表
  useEffect(() => {
    fetchApps();
  }, [fetchApps]);

  return {
    apps,
    currentRunId,
    isLoading,
    error,
    setCurrentRunId,
    fetchApps,
    getAppStats,
    createApp,
    deleteApp
  };
};
