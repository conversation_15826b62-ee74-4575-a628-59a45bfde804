import { useState, useCallback, useEffect } from 'react';
import { realMem0Client } from '@/lib/mem0-client';

export interface AgentInfo {
  id: string;
  name: string;
  memory_count: number;
  last_active?: string;
  created_at?: string;
  description?: string;
  type?: string;
}

interface UseAgentManagementReturn {
  agents: AgentInfo[];
  currentAgentId: string | null;
  isLoading: boolean;
  error: string | null;
  setCurrentAgentId: (agentId: string) => void;
  fetchAgents: () => Promise<void>;
  getAgentStats: (agentId: string) => Promise<any>;
  createAgent: (agentId: string, name?: string, description?: string, type?: string) => Promise<void>;
  deleteAgent: (agentId: string) => Promise<void>;
}

export const useAgentManagement = (): UseAgentManagementReturn => {
  const [agents, setAgents] = useState<AgentInfo[]>([]);
  const [currentAgentId, setCurrentAgentIdState] = useState<string | null>(
    typeof window !== 'undefined' ? localStorage.getItem('mem0_current_agent_id') : null
  );
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const setCurrentAgentId = useCallback((agentId: string) => {
    setCurrentAgentIdState(agentId);
    if (typeof window !== 'undefined') {
      localStorage.setItem('mem0_current_agent_id', agentId);
    }
  }, []);

  const fetchAgents = useCallback(async () => {
    setIsLoading(true);
    setError(null);
    try {
      // 由于Mem0 API没有直接的智能体列表端点，我们通过获取记忆来推断智能体
      const memories = await realMem0Client.getMemories({ 
        user_id: userId, 
        limit: 1000 
      });
      
      // 从记忆中提取唯一的agent_id
      const agentIdMap = new Map<string, AgentInfo>();
      
      memories.memories.forEach(memory => {
        const agentId = memory.agent_id || 'default';
        if (!agentIdMap.has(agentId)) {
          agentIdMap.set(agentId, {
            id: agentId,
            name: agentId === 'default' ? '默认智能体' : `智能体 ${agentId}`,
            memory_count: 0,
            last_active: memory.updated_at || memory.created_at || new Date().toISOString(),
            created_at: memory.created_at || new Date().toISOString(),
            description: agentId === 'default' ? '默认智能体实例' : `智能体 ${agentId}`,
            type: agentId === 'default' ? 'system' : 'custom'
          });
        }
        
        const agent = agentIdMap.get(agentId)!;
        agent.memory_count++;
        
        // 更新最后活跃时间
        const memoryTime = memory.updated_at || memory.created_at;
        if (memoryTime && (!agent.last_active || memoryTime > agent.last_active)) {
          agent.last_active = memoryTime;
        }
      });

      // 如果没有找到任何智能体，添加默认智能体
      if (agentIdMap.size === 0) {
        agentIdMap.set('default', {
          id: 'default',
          name: '默认智能体',
          memory_count: 0,
          last_active: new Date().toISOString(),
          created_at: new Date().toISOString(),
          description: '默认智能体实例',
          type: 'system'
        });
      }

      // 如果有当前agentId且不在列表中，添加它
      if (currentAgentId && !agentIdMap.has(currentAgentId)) {
        const agentStats = await getAgentStats(currentAgentId);
        agentIdMap.set(currentAgentId, {
          id: currentAgentId,
          name: `智能体 ${currentAgentId}`,
          memory_count: agentStats.total_memories || 0,
          last_active: new Date().toISOString(),
          created_at: new Date().toISOString(),
          description: `智能体 ${currentAgentId}`,
          type: 'custom'
        });
      }

      const agentsList = Array.from(agentIdMap.values()).sort((a, b) => {
        // 默认智能体排在前面
        if (a.id === 'default') return -1;
        if (b.id === 'default') return 1;
        // 按最后活跃时间排序
        return (b.last_active || '').localeCompare(a.last_active || '');
      });

      setAgents(agentsList);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch agents';
      setError(errorMessage);
      console.error('Failed to fetch agents:', err);
    } finally {
      setIsLoading(false);
    }
  }, [currentAgentId]);

  const getAgentStats = useCallback(async (agentId: string) => {
    try {
      // 获取特定agent_id的记忆统计
      const memories = await realMem0Client.getMemories({ agent_id: agentId, limit: 1 });
      return { total_memories: memories.total };
    } catch (err) {
      console.error(`Failed to get stats for agent ${agentId}:`, err);
      return { total_memories: 0 };
    }
  }, []);

  const createAgent = useCallback(async (agentId: string, name?: string, description?: string, type?: string) => {
    setIsLoading(true);
    setError(null);
    try {
      // 通过创建一个初始记忆来"创建"智能体
      await realMem0Client.createMemory({
        messages: [{ role: 'system', content: `Agent ${agentId} initialized` }],
        agent_id: agentId,
        metadata: { 
          agent_name: name || `智能体 ${agentId}`, 
          description: description,
          agent_type: type || 'custom',
          is_initialization: true 
        }
      });

      // 更新智能体列表
      await fetchAgents();
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to create agent';
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setIsLoading(false);
    }
  }, [fetchAgents]);

  const deleteAgent = useCallback(async (agentId: string) => {
    setIsLoading(true);
    setError(null);
    try {
      // 删除智能体的所有记忆
      await realMem0Client.deleteAllMemories({ agent_id: agentId });
      
      // 如果删除的是当前智能体，清除当前agentId
      if (currentAgentId === agentId) {
        setCurrentAgentId('');
      }

      // 更新智能体列表
      await fetchAgents();
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to delete agent';
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setIsLoading(false);
    }
  }, [currentAgentId, setCurrentAgentId, fetchAgents]);

  // 初始化时获取智能体列表
  useEffect(() => {
    fetchAgents();
  }, [fetchAgents]);

  return {
    agents,
    currentAgentId,
    isLoading,
    error,
    setCurrentAgentId,
    fetchAgents,
    getAgentStats,
    createAgent,
    deleteAgent
  };
};
