# OpenMemory UI

A modern web interface for OpenMemory, built with Next.js 15, React, and TypeScript. Features a comprehensive Graph Memory visualization system for managing and exploring memory relationships.

## ✨ Features

### Core Functionality
- 🧠 **Memory Management**: Create, view, and organize your memories
- 🔍 **Advanced Search**: Powerful search capabilities across your memory collection
- 📊 **Analytics**: Insights and statistics about your memory usage
- 🎨 **Modern UI**: Clean, responsive design with dark/light theme support
- ⚡ **Performance**: Optimized for speed with Next.js 15 and React 19

### Graph Memory System (New!)
- 🕸️ **Graph Visualization**: Interactive graph-based memory exploration using React Flow
- 🎯 **Entity Management**: Create and manage entities (people, organizations, concepts, events)
- 🔗 **Relationship Mapping**: Define and visualize relationships between entities
- 📈 **Performance Optimized**: Handles large datasets with virtualization and LOD rendering
- 📱 **Mobile Responsive**: Touch-optimized interface for mobile devices
- 🔍 **Semantic Search**: AI-powered semantic search across graph data
- 📊 **Real-time Statistics**: Live statistics and performance monitoring
- 🎛️ **Advanced Filtering**: Multi-dimensional filtering and search capabilities
- 📱 **Batch Operations**: Efficient bulk operations for large datasets
- 🛡️ **Error Boundaries**: Robust error handling and recovery mechanisms

## 🛠️ Tech Stack

- **Framework**: Next.js 15 with App Router
- **Frontend**: React 19, TypeScript
- **Graph Visualization**: React Flow 11.10.1
- **Styling**: Tailwind CSS, shadcn/ui components
- **State Management**: Redux Toolkit
- **API Integration**: Custom Mem0 API client with Graph Memory support
- **Performance**: Custom performance management system
- **Development**: ESLint, Prettier, Husky

## 🚀 Getting Started

### Prerequisites

- Node.js 18+ 
- npm or yarn or pnpm

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd openmemory/ui
```

2. Install dependencies:
```bash
npm install
# or
yarn install
# or
pnpm install
```

3. Set up environment variables:
```bash
cp .env.example .env.local
```

Edit `.env.local` with your configuration:
```env
NEXT_PUBLIC_MEM0_API_URL=your_mem0_api_url
NEXT_PUBLIC_MEM0_API_KEY=your_mem0_api_key
```

4. Run the development server:
```bash
npm run dev
# or
yarn dev
# or
pnpm dev
```

5. Open [http://localhost:3000](http://localhost:3000) in your browser.

## 📁 Project Structure

```
openmemory/ui/
├── app/                    # Next.js App Router pages
│   ├── apps/              # Graph Memory main page
│   ├── memories/          # Traditional memory management
│   └── settings/          # Application settings
├── components/             # Reusable React components
│   ├── graph/             # Graph Memory components
│   ├── memory/            # Memory management components
│   ├── ui/                # shadcn/ui components
│   └── common/            # Shared components
├── hooks/                  # Custom React hooks
│   └── useGraphMemoryApi.ts # Graph Memory API hook
├── lib/                    # Utility functions and configurations
│   ├── performance/       # Performance management
│   └── graph-data-transformer.ts # Data transformation utilities
├── store/                  # Redux store and slices
│   └── graphMemorySlice.ts # Graph Memory state management
├── styles/                 # Global styles and CSS
│   ├── animation.css      # Animation styles
│   └── graph-performance.css # Graph performance optimizations
├── types/                  # TypeScript type definitions
│   └── graph-memory.ts    # Graph Memory type definitions
├── docs/                   # Documentation
│   └── components/        # Component documentation
│       └── graph-memory/  # Graph Memory documentation
└── public/                 # Static assets
```

## 📚 Documentation

### Graph Memory System
- [Overview](./docs/components/graph-memory/README.md) - Complete system overview
- [GraphVisualization](./docs/components/graph-memory/GraphVisualization.md) - Core visualization component
- [useGraphMemoryApi](./docs/components/graph-memory/useGraphMemoryApi.md) - API management hook

### Quick Start Examples

#### Basic Graph Visualization
```tsx
import GraphVisualization from '@/components/graph/GraphVisualization';

export default function MyGraphPage() {
  return (
    <div className="w-full h-screen">
      <GraphVisualization 
        height="100vh"
        onNodeClick={(node) => console.log('Node clicked:', node)}
      />
    </div>
  );
}
```

#### Using the API Hook
```tsx
import { useGraphMemoryApi } from '@/hooks/useGraphMemoryApi';

export default function GraphComponent() {
  const { fetchGraphMemories, createEntity, isLoading } = useGraphMemoryApi();
  
  const handleCreateEntity = async () => {
    const entity = await createEntity({
      name: 'New Entity',
      type: 'person',
      properties: { description: 'A new person entity' }
    });
    console.log('Created:', entity);
  };

  return (
    <button onClick={handleCreateEntity} disabled={isLoading}>
      {isLoading ? 'Creating...' : 'Create Entity'}
    </button>
  );
}
```

## 🎯 Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run lint` - Run ESLint
- `npm run type-check` - Run TypeScript type checking

## 🏗️ Architecture

### Graph Memory System Architecture
```
┌─────────────────────────────────────────────────────────────┐
│                    Presentation Layer                       │
├─────────────────────────────────────────────────────────────┤
│ GraphVisualization │ EntityPanel │ RelationshipPanel │ ... │
├─────────────────────────────────────────────────────────────┤
│                    Business Logic Layer                     │
├─────────────────────────────────────────────────────────────┤
│ useGraphMemoryApi │ Performance Manager │ Cache Manager    │
├─────────────────────────────────────────────────────────────┤
│                      Data Layer                             │
├─────────────────────────────────────────────────────────────┤
│ Redux Store │ Mem0 API Client │ Data Transformers          │
└─────────────────────────────────────────────────────────────┘
```

### Key Features
- **High Performance**: Handles 1000+ nodes with smooth interactions
- **Mobile Optimized**: Touch gestures and responsive design
- **Error Resilient**: Comprehensive error boundaries and recovery
- **Cache Intelligent**: Smart caching with TTL and size management
- **Type Safe**: Full TypeScript coverage with strict type checking

## 🤝 Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

### Development Guidelines
- Follow the existing code style and patterns
- Add tests for new features
- Update documentation for API changes
- Ensure TypeScript types are properly defined
- Test on both desktop and mobile devices

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- [React Flow](https://reactflow.dev/) for the excellent graph visualization library
- [shadcn/ui](https://ui.shadcn.com/) for the beautiful UI components
- [Mem0](https://mem0.ai/) for the powerful memory management API
