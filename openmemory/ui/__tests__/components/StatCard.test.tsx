import React from 'react';
import { render, screen } from '@testing-library/react';
import { Brain } from 'lucide-react';
import StatCard from '@/components/mem0/StatCard';

describe('StatCard', () => {
  it('renders basic stat card correctly', () => {
    render(
      <StatCard
        title="Test Stat"
        value={100}
        icon={Brain}
      />
    );

    expect(screen.getByText('100')).toBeInTheDocument();
    expect(screen.getByText('Test Stat')).toBeInTheDocument();
  });

  it('renders loading state correctly', () => {
    render(
      <StatCard
        title="Test Stat"
        value={100}
        icon={Brain}
        isLoading={true}
      />
    );

    // 在加载状态下，不应该显示实际的值和标题
    expect(screen.queryByText('100')).not.toBeInTheDocument();
    expect(screen.queryByText('Test Stat')).not.toBeInTheDocument();
  });

  it('renders trend information correctly', () => {
    render(
      <StatCard
        title="Test Stat"
        value={100}
        icon={Brain}
        trend={{ value: 15, isPositive: true }}
      />
    );

    expect(screen.getByText('15%')).toBeInTheDocument();
    expect(screen.getByText('↗')).toBeInTheDocument();
  });

  it('renders negative trend correctly', () => {
    render(
      <StatCard
        title="Test Stat"
        value={100}
        icon={Brain}
        trend={{ value: 10, isPositive: false }}
      />
    );

    expect(screen.getByText('10%')).toBeInTheDocument();
    expect(screen.getByText('↘')).toBeInTheDocument();
  });

  it('renders subtitle when provided', () => {
    render(
      <StatCard
        title="Test Stat"
        value={100}
        icon={Brain}
        subtitle="Additional info"
      />
    );

    expect(screen.getByText('Additional info')).toBeInTheDocument();
  });
});
