import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import Mem0StatsDashboard from '@/components/mem0/Mem0StatsDashboard';

// Mock the realMem0Client
jest.mock('@/lib/mem0-client/realClient', () => ({
  realMem0Client: {
    getMemories: jest.fn()
  }
}));

import { realMem0Client } from '@/lib/mem0-client/realClient';

describe('Mem0StatsDashboard', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders dashboard title correctly', () => {
    (realMem0Client.getMemories as jest.Mock).mockResolvedValue({
      memories: [],
      total: 0
    });

    render(<Mem0StatsDashboard />);
    
    expect(screen.getByText('Mem0 统计面板')).toBeInTheDocument();
    expect(screen.getByText('实时数据')).toBeInTheDocument();
  });

  it('renders all stat cards', async () => {
    const mockMemories = [
      {
        id: '1',
        memory: 'Test memory 1',
        user_id: 'user1',
        created_at: new Date().toISOString()
      },
      {
        id: '2',
        memory: 'Test memory 2',
        user_id: 'user2',
        created_at: new Date().toISOString()
      }
    ];

    (realMem0Client.getMemories as jest.Mock).mockResolvedValue({
      memories: mockMemories,
      total: 2
    });

    render(<Mem0StatsDashboard />);

    await waitFor(() => {
      expect(screen.getByText('总记忆数')).toBeInTheDocument();
      expect(screen.getByText('今日操作')).toBeInTheDocument();
      expect(screen.getByText('平均响应时间')).toBeInTheDocument();
      expect(screen.getByText('活跃用户')).toBeInTheDocument();
    });
  });

  it('renders quick actions section', () => {
    (realMem0Client.getMemories as jest.Mock).mockResolvedValue({
      memories: [],
      total: 0
    });

    render(<Mem0StatsDashboard />);
    
    expect(screen.getByText('快速操作')).toBeInTheDocument();
    expect(screen.getByText('创建记忆')).toBeInTheDocument();
    expect(screen.getByText('搜索记忆')).toBeInTheDocument();
    expect(screen.getByText('用户管理')).toBeInTheDocument();
    expect(screen.getByText('刷新数据')).toBeInTheDocument();
  });

  it('handles API error gracefully', async () => {
    (realMem0Client.getMemories as jest.Mock).mockRejectedValue(
      new Error('API Error')
    );

    render(<Mem0StatsDashboard />);

    await waitFor(() => {
      // 当API失败时，组件会显示默认数据而不是错误信息
      // 因为它有fallback逻辑
      expect(screen.getByText('总记忆数')).toBeInTheDocument();
    }, { timeout: 3000 });
  });

  it('shows loading state initially', () => {
    (realMem0Client.getMemories as jest.Mock).mockImplementation(
      () => new Promise(() => {}) // Never resolves
    );

    render(<Mem0StatsDashboard />);
    
    // Should show loading state for stat cards
    const loadingElements = screen.getAllByTestId('loading-card');
    expect(loadingElements.length).toBeGreaterThan(0);
  });
});
