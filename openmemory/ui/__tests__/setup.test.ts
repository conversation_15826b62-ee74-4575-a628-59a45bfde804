/**
 * 基础测试环境验证
 */
describe('测试环境配置验证', () => {
  test('Jest测试环境正常运行', () => {
    expect(true).toBe(true);
  });

  test('TypeScript类型检查正常', () => {
    const testString: string = 'Hello TypeScript';
    expect(typeof testString).toBe('string');
  });

  test('模块路径别名正常工作', () => {
    // 这个测试验证@/路径别名是否正确配置
    expect(() => {
      // 如果路径别名配置正确，这个导入不会报错
      const mockPath = '@/components/ui/button';
      expect(mockPath).toContain('@/');
    }).not.toThrow();
  });
});
