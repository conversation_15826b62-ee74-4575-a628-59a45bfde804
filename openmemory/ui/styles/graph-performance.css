/**
 * Graph Memory 性能优化样式
 * 
 * 包含针对图可视化组件的性能优化CSS：
 * - 硬件加速
 * - GPU渲染优化
 * - 动画性能优化
 * - 移动端触摸优化
 * - LOD渲染样式
 */

/* 硬件加速基础类 */
.gpu-accelerated {
  transform: translateZ(0);
  will-change: transform;
  backface-visibility: hidden;
  perspective: 1000px;
}

/* 图可视化容器优化 */
.graph-visualization-container {
  contain: layout style paint;
  transform: translateZ(0);
  will-change: transform;
}

/* React Flow 性能优化 */
.react-flow__renderer {
  transform: translateZ(0);
  will-change: transform;
}

.react-flow__viewport {
  transform: translateZ(0);
  will-change: transform;
}

/* 节点性能优化 */
.graph-node {
  contain: layout style paint;
  transform: translateZ(0);
  will-change: transform, opacity;
  backface-visibility: hidden;
  
  /* 减少重绘 */
  border-radius: 8px;
  transition: transform 0.2s ease-out, opacity 0.2s ease-out;
}

.graph-node:hover {
  transform: translateZ(0) scale(1.05);
}

/* LOD 级别样式 */
.graph-node.minimal {
  transform: translateZ(0) scale(0.8);
  opacity: 0.7;
}

.graph-node.minimal .node-label,
.graph-node.minimal .node-description {
  display: none;
}

.graph-node.low-detail {
  transform: translateZ(0) scale(0.9);
  opacity: 0.85;
}

.graph-node.low-detail .node-description {
  display: none;
}

.graph-node.medium-detail {
  transform: translateZ(0);
  opacity: 0.95;
}

.graph-node.high-detail {
  transform: translateZ(0);
  opacity: 1;
}

/* 边性能优化 */
.react-flow__edge {
  will-change: opacity;
  transition: opacity 0.2s ease-out;
}

.react-flow__edge.simplified {
  opacity: 0.5;
  stroke-width: 1px;
}

/* 虚拟化渲染器优化 */
.virtualized-graph-renderer {
  contain: layout style paint;
  transform: translateZ(0);
  will-change: transform;
  overflow: hidden;
}

.virtualized-graph-renderer .edges-layer {
  pointer-events: none;
  transform: translateZ(0);
  will-change: transform;
}

.virtualized-graph-renderer .nodes-layer {
  transform: translateZ(0);
  will-change: transform;
  contain: layout style paint;
}

/* 移动端优化 */
@media (max-width: 768px) {
  .graph-node {
    /* 移动端减少动画以提升性能 */
    transition: none;
  }
  
  .graph-node:hover {
    transform: translateZ(0);
  }
  
  /* 移动端简化样式 */
  .react-flow__controls {
    transform: translateZ(0);
    will-change: transform;
  }
  
  .react-flow__minimap {
    display: none; /* 移动端隐藏小地图以节省性能 */
  }
}

/* 触摸优化 */
.mobile-graph-interface {
  touch-action: pan-x pan-y;
  -webkit-overflow-scrolling: touch;
  transform: translateZ(0);
  will-change: transform;
}

.mobile-graph-interface .graph-node {
  /* 增大触摸目标 */
  min-width: 44px;
  min-height: 44px;
  cursor: pointer;
}

/* 性能监控组件样式 */
.performance-monitor {
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  transform: translateZ(0);
  will-change: transform, opacity;
}

/* 加载状态优化 */
.graph-loading {
  contain: layout style paint;
  transform: translateZ(0);
}

.graph-loading .loading-spinner {
  animation: spin 1s linear infinite;
  transform: translateZ(0);
  will-change: transform;
}

@keyframes spin {
  from {
    transform: translateZ(0) rotate(0deg);
  }
  to {
    transform: translateZ(0) rotate(360deg);
  }
}

/* 错误边界样式 */
.graph-error-boundary {
  contain: layout style paint;
  transform: translateZ(0);
}

/* 缓存优化指示器 */
.cache-indicator {
  position: absolute;
  top: 4px;
  right: 4px;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  transform: translateZ(0);
  will-change: background-color;
  transition: background-color 0.2s ease-out;
}

.cache-indicator.hit {
  background-color: #10b981; /* green-500 */
}

.cache-indicator.miss {
  background-color: #f59e0b; /* amber-500 */
}

.cache-indicator.loading {
  background-color: #6b7280; /* gray-500 */
  animation: pulse 1s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  .graph-node {
    border: 2px solid currentColor;
  }
  
  .react-flow__edge {
    stroke-width: 3px;
  }
}

/* 减少动画偏好支持 */
@media (prefers-reduced-motion: reduce) {
  .graph-node,
  .react-flow__edge,
  .loading-spinner,
  .cache-indicator {
    animation: none;
    transition: none;
  }
}

/* 深色模式优化 */
@media (prefers-color-scheme: dark) {
  .graph-node {
    /* 深色模式下的优化 */
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  }
}

/* 打印样式优化 */
@media print {
  .performance-monitor,
  .react-flow__controls,
  .react-flow__minimap {
    display: none;
  }
  
  .graph-node,
  .react-flow__edge {
    /* 打印时移除动画和变换 */
    transform: none;
    transition: none;
    animation: none;
  }
}

/* 内存优化类 */
.memory-optimized {
  /* 限制重绘区域 */
  contain: layout style paint;
  
  /* 优化合成层 */
  transform: translateZ(0);
  will-change: auto;
  
  /* 减少内存占用 */
  image-rendering: optimizeSpeed;
  image-rendering: -webkit-optimize-contrast;
  image-rendering: optimize-contrast;
}

/* 批量更新优化 */
.batch-updating {
  pointer-events: none;
  opacity: 0.8;
  transition: opacity 0.1s ease-out;
}

.batch-updating.complete {
  pointer-events: auto;
  opacity: 1;
}

/* 视口外元素优化 */
.offscreen {
  visibility: hidden;
  transform: translateZ(0);
  will-change: auto;
}

/* 高性能滚动 */
.high-performance-scroll {
  overflow: auto;
  -webkit-overflow-scrolling: touch;
  scroll-behavior: smooth;
  contain: layout style paint;
}

/* GPU 内存优化 */
.gpu-memory-optimized {
  /* 限制GPU内存使用 */
  transform: translateZ(0);
  will-change: transform;
  backface-visibility: hidden;
  
  /* 避免创建过多合成层 */
  isolation: isolate;
}
