/**
 * 真实Mem0 API客户端
 * 基于http://localhost:8000/openapi.json规范
 */

import axios, { AxiosInstance, AxiosResponse } from 'axios';
import {
  Mem0Memory,
  MemoryCreateRequest,
  UpdateMemoryRequest,
  SearchRequest,
  BatchUpdateRequest,
  BatchDeleteRequest,
  HTTPValidationError,
  Mem0ClientConfig,
  HealthCheckResponse,
  FeedbackRequest,
  // Graph Memory 相关类型
  Mem0GraphMemoryResponse,
  Mem0GraphSearchRequest,
  GraphEntityCreateRequest,
  GraphEntityUpdateRequest,
  GraphRelationCreateRequest,
  GraphRelationUpdateRequest,
  GraphBatchUpdateRequest,
  GraphBatchOperationResponse,
  GraphMemoryStats,
  Mem0GraphEntity,
  Mem0GraphRelation,
  // P0 Priority UI Management APIs
  UIStatsResponse,
  UIActivitiesResponse,
  UIDashboardResponse,
  UIStatsQueryParams,
  UIActivitiesQueryParams,
  // P1 Priority User Management APIs
  UIUsersResponse,
  UIUserStats,
  UIUserDeleteResponse,
  UIUsersQueryParams,
  // P2 Priority Graph Management APIs
  UIGraphEntitiesResponse,
  UIGraphRelationshipsResponse,
  UIGraphEntitiesQueryParams,
  UIGraphRelationshipsQueryParams
} from '@/types/mem0-api';

export class RealMem0APIClient {
  private client: AxiosInstance;
  private config: Mem0ClientConfig;

  constructor(config: Mem0ClientConfig) {
    this.config = {
      timeout: 30000,
      retries: 3,
      ...config
    };

    this.client = axios.create({
      baseURL: this.config.baseUrl,
      timeout: this.config.timeout || 30000,
      headers: {
        'Content-Type': 'application/json',
        ...(this.config.apiKey && { 'Authorization': `Bearer ${this.config.apiKey}` })
      }
    });

    // 添加响应拦截器处理错误
    this.client.interceptors.response.use(
      (response) => response,
      (error) => {
        if (error.response?.data?.detail) {
          // 处理FastAPI验证错误格式
          const validationError: HTTPValidationError = error.response.data;
          const errorMessage = validationError.detail
            .map(err => `${err.loc.join('.')}: ${err.msg}`)
            .join(', ');
          throw new Error(`Validation Error: ${errorMessage}`);
        }
        if (error.response?.data?.message) {
          throw new Error(error.response.data.message);
        }
        throw new Error(error.message || 'Network Error');
      }
    );
  }

  // 健康检查
  async healthCheck(): Promise<HealthCheckResponse> {
    const response = await this.client.get<HealthCheckResponse>('/health');
    return response.data;
  }

  // 记忆管理方法 - 基于真实API格式
  async getMemories(params?: {
    user_id?: string;
    agent_id?: string;
    run_id?: string;
    limit?: number;
    enable_graph?: boolean;
    output_format?: string;
  }): Promise<{ memories: Mem0Memory[]; total: number; limit: number; offset: number }> {
    const response = await this.client.get('/v1/memories/', { params });
    return response.data;
  }

  async createMemory(data: MemoryCreateRequest): Promise<{ message: string; memory: Mem0Memory }> {
    const response = await this.client.post('/v1/memories/', data);
    return response.data;
  }

  async getMemory(id: string): Promise<Mem0Memory> {
    const response = await this.client.get(`/v1/memories/${id}/`);
    return response.data;
  }

  async updateMemory(id: string, data: UpdateMemoryRequest): Promise<{ message: string; memory: Mem0Memory }> {
    const response = await this.client.put(`/v1/memories/${id}/`, data);
    return response.data;
  }

  async deleteMemory(id: string): Promise<{ message: string }> {
    const response = await this.client.delete(`/v1/memories/${id}/`);
    return response.data;
  }

  async searchMemories(data: SearchRequest): Promise<{ memories: Mem0Memory[]; total: number; query: string }> {
    const response = await this.client.post('/v1/memories/search/', data);
    return response.data;
  }

  async deleteAllMemories(params?: {
    user_id?: string;
    agent_id?: string;
    run_id?: string;
  }): Promise<{ message: string; deleted_count: number }> {
    const response = await this.client.delete('/v1/memories/', { params });
    return response.data;
  }

  // 批量操作
  async batchUpdateMemories(data: BatchUpdateRequest): Promise<{ success_count: number; failed_count: number; errors?: Array<{ memory_id: string; error: string }> }> {
    const response = await this.client.put('/v1/batch/', data);
    return response.data;
  }

  async batchDeleteMemories(data: BatchDeleteRequest): Promise<{ success_count: number; failed_count: number; errors?: Array<{ memory_id: string; error: string }> }> {
    const response = await this.client.delete('/v1/batch/', { data });
    return response.data;
  }

  // 导出功能
  async exportMemories(params?: {
    user_id?: string;
    agent_id?: string;
    run_id?: string;
    format?: 'json' | 'csv';
  }): Promise<{ export_id: string; message: string }> {
    const response = await this.client.post('/v1/exports/', params);
    return response.data;
  }

  async getExportStatus(exportId: string): Promise<{ status: string; download_url?: string }> {
    const response = await this.client.get(`/v1/exports/${exportId}/`);
    return response.data;
  }

  // 统计方法
  async getStats(user_id?: string): Promise<any> {
    const params = user_id ? { user_id } : {};
    const response = await this.client.get('/v1/stats/', { params });
    return response.data;
  }

  // ========================================================================
  // P0 Priority UI Management APIs (服务器已实现)
  // ========================================================================

  /**
   * 获取系统统计数据 - UI专用API
   * 支持用户过滤和时间范围查询
   */
  async getUIStats(params?: UIStatsQueryParams): Promise<UIStatsResponse> {
    const response = await this.client.get('/v1/stats', { params });
    return response.data;
  }

  /**
   * 获取活动日志 - UI专用API
   * 支持分页、过滤和时间范围查询
   */
  async getUIActivities(params?: UIActivitiesQueryParams): Promise<UIActivitiesResponse> {
    const response = await this.client.get('/v1/activities', { params });
    return response.data;
  }

  /**
   * 获取管理面板数据 - UI专用API
   * 一次性获取统计数据、最近活动和快速操作
   */
  async getUIDashboard(params?: { 
    user_id?: string; 
    time_range?: '1h' | '24h' | '7d' | '30d' 
  }): Promise<UIDashboardResponse> {
    const response = await this.client.get('/v1/admin/dashboard', { params });
    return response.data;
  }

  // ========================================================================
  // P1 Priority User Management APIs (用户管理API方法)
  // ========================================================================
  
  /**
   * 获取用户列表 - 支持分页和统计信息
   */
  async getUsers(params?: UIUsersQueryParams): Promise<UIUsersResponse> {
    const response = await this.client.get('/v1/users', { params });
    return response.data;
  }

  /**
   * 获取特定用户的详细统计信息
   */
  async getUserStats(userId: string, timeRange?: '1h' | '24h' | '7d' | '30d'): Promise<UIUserStats> {
    const response = await this.client.get(`/v1/users/${userId}/stats`, { 
      params: { time_range: timeRange } 
    });
    return response.data;
  }

  /**
   * 删除用户及其所有关联数据
   */
  async deleteUser(userId: string): Promise<UIUserDeleteResponse> {
    const response = await this.client.delete(`/v1/users/${userId}`);
    return response.data;
  }

  // ========================================================================
  // P2 Priority Graph Management APIs (图管理API方法)
  // ========================================================================
  
  /**
   * 获取图实体列表
   */
  async getGraphEntities(params?: UIGraphEntitiesQueryParams): Promise<UIGraphEntitiesResponse> {
    const response = await this.client.get('/v1/graph/entities', { params });
    return response.data;
  }

  /**
   * 获取图关系列表
   */
  async getGraphRelationships(params?: UIGraphRelationshipsQueryParams): Promise<UIGraphRelationshipsResponse> {
    const response = await this.client.get('/v1/graph/relationships', { params });
    return response.data;
  }

  // 反馈方法
  async submitFeedback(data: FeedbackRequest): Promise<{ message: string }> {
    const response = await this.client.post('/v1/feedback/', data);
    return response.data;
  }

  // 获取记忆历史
  async getMemoryHistory(memoryId: string): Promise<any> {
    const response = await this.client.get(`/v1/memories/${memoryId}/history/`);
    return response.data;
  }

  // 重置所有记忆
  async resetMemories(): Promise<{ message: string; total_memories: number }> {
    const response = await this.client.post('/reset');
    return response.data;
  }

  // 配置Mem0
  async configure(config: Record<string, any>): Promise<any> {
    const response = await this.client.post('/configure', config);
    return response.data;
  }

  // 缓存管理
  async clearCache(): Promise<any> {
    const response = await this.client.post('/cache/clear');
    return response.data;
  }

  async getCacheStatus(): Promise<any> {
    const response = await this.client.get('/cache/status');
    return response.data;
  }

  // ========================================================================
  // Graph Memory API 方法
  // ========================================================================

  /**
   * 获取Graph Memory数据
   * 支持筛选条件和分页
   * 基于实际API端点：/v1/graph/entities 和 /v1/graph/relationships
   */
  async getGraphMemories(params?: {
    user_id?: string;
    agent_id?: string;
    run_id?: string;
    entity_types?: string[];
    relation_types?: string[];
    limit?: number;
    offset?: number;
  }): Promise<Mem0GraphMemoryResponse> {
    // 并行获取实体和关系数据
    const [entitiesResponse, relationshipsResponse] = await Promise.all([
      this.client.get('/v1/graph/entities', {
        params: {
          user_id: params?.user_id,
          entity_type: params?.entity_types?.[0], // API只支持单个类型
          limit: Math.min(params?.limit || 100, 500)
        }
      }),
      this.client.get('/v1/graph/relationships', {
        params: {
          user_id: params?.user_id,
          limit: Math.min(params?.limit || 100, 500)
        }
      })
    ]);

    // 转换为统一的Graph Memory响应格式
    return {
      entities: entitiesResponse.data.entities || [],
      relations: relationshipsResponse.data.relationships || [],
      totalEntities: entitiesResponse.data.total || 0,
      total_relations: relationshipsResponse.data.total || 0,
      limit: params?.limit || 100,
      offset: params?.offset || 0
    };
  }

  /**
   * 搜索Graph Memory - 注意：当前API不支持图搜索
   * 使用基础的实体和关系过滤来模拟搜索
   */
  async searchGraphMemories(data: Mem0GraphSearchRequest): Promise<Mem0GraphMemoryResponse> {
    // 使用基础的getGraphMemories来模拟搜索
    return this.getGraphMemories({
      user_id: data.user_id,
      limit: data.limit || 100
    });
  }

  /**
   * 获取Graph Memory统计信息
   * 基于实际API端点计算统计数据
   */
  async getGraphStats(params?: {
    user_id?: string;
    agent_id?: string;
    run_id?: string;
  }): Promise<GraphMemoryStats> {
    try {
      // 并行获取实体和关系数据来计算统计信息
      const [entitiesResponse, relationshipsResponse] = await Promise.all([
        this.client.get('/v1/graph/entities', {
          params: { user_id: params?.user_id, limit: 1000 }
        }),
        this.client.get('/v1/graph/relationships', {
          params: { user_id: params?.user_id, limit: 1000 }
        })
      ]);

      const entities = entitiesResponse.data.entities || [];
      const relations = relationshipsResponse.data.relationships || [];

      // 计算统计信息
      const entityTypesCount: Record<string, number> = {};
      const relationTypesCount: Record<string, number> = {};

      entities.forEach((entity: any) => {
        const type = entity.type || 'unknown';
        entityTypesCount[type] = (entityTypesCount[type] || 0) + 1;
      });

      relations.forEach((relation: any) => {
        const type = relation.relationship_type || 'unknown';
        relationTypesCount[type] = (relationTypesCount[type] || 0) + 1;
      });

      const totalEntities = entities.length;
      const totalRelations = relations.length;
      const graphDensity = totalEntities > 0 ? totalRelations / (totalEntities * (totalEntities - 1)) : 0;
      const avgConnectionsPerEntity = totalEntities > 0 ? (totalRelations * 2) / totalEntities : 0;

      return {
        total_entities: totalEntities,
        total_relations: totalRelations,
        graph_density: Math.min(graphDensity, 1), // 确保密度不超过1
        active_entities: totalEntities, // 假设所有实体都是活跃的
        entity_types_count: entityTypesCount,
        relation_types_count: relationTypesCount,
        avg_connections_per_entity: avgConnectionsPerEntity,
        most_connected_entities: [] // 暂时返回空数组，可以后续实现
      };
    } catch (error) {
      console.error('Failed to fetch graph stats:', error);
      // 返回默认的空统计信息
      return {
        total_entities: 0,
        total_relations: 0,
        graph_density: 0,
        active_entities: 0,
        entity_types_count: {},
        relation_types_count: {},
        avg_connections_per_entity: 0,
        most_connected_entities: []
      };
    }
  }

  // ========================================================================
  // Graph Entity 管理方法
  // ========================================================================

  /**
   * 创建新实体 - 注意：当前API不支持直接创建实体
   * 实体通过记忆创建时自动生成
   */
  async createGraphEntity(data: GraphEntityCreateRequest): Promise<{ message: string; entity: Mem0GraphEntity }> {
    // 当前API不支持直接创建实体，返回模拟响应
    throw new Error('Direct entity creation not supported. Entities are created automatically through memory creation with enable_graph=true.');
  }

  /**
   * 获取单个实体详情 - 注意：当前API不支持单个实体查询
   */
  async getGraphEntity(id: string): Promise<Mem0GraphEntity> {
    // 通过获取所有实体然后过滤来模拟单个实体查询
    const response = await this.client.get('/v1/graph/entities', {
      params: { limit: 1000 }
    });
    const entities = response.data.entities || [];
    const entity = entities.find((e: any) => e.id === id);

    if (!entity) {
      throw new Error(`Entity with id ${id} not found`);
    }

    return entity;
  }

  /**
   * 更新实体信息 - 注意：当前API不支持直接更新实体
   */
  async updateGraphEntity(id: string, data: GraphEntityUpdateRequest): Promise<{ message: string; entity: Mem0GraphEntity }> {
    throw new Error('Direct entity update not supported. Entities are updated through memory updates.');
  }

  /**
   * 删除实体 - 注意：当前API不支持直接删除实体
   */
  async deleteGraphEntity(id: string): Promise<{ message: string }> {
    throw new Error('Direct entity deletion not supported. Entities are deleted through memory deletion.');
  }

  /**
   * 获取实体的所有关系
   */
  async getEntityRelations(id: string): Promise<{ relations: Mem0GraphRelation[] }> {
    const response = await this.client.get('/v1/graph/relationships', {
      params: { source_entity: id, limit: 1000 }
    });
    return { relations: response.data.relationships || [] };
  }

  // ========================================================================
  // Graph Relation 管理方法
  // ========================================================================

  /**
   * 创建新关系 - 注意：当前API不支持直接创建关系
   */
  async createGraphRelation(data: GraphRelationCreateRequest): Promise<{ message: string; relation: Mem0GraphRelation }> {
    throw new Error('Direct relation creation not supported. Relations are created automatically through memory creation with enable_graph=true.');
  }

  /**
   * 获取单个关系详情
   */
  async getGraphRelation(id: string): Promise<Mem0GraphRelation> {
    const response = await this.client.get('/v1/graph/relationships', {
      params: { limit: 1000 }
    });
    const relations = response.data.relationships || [];
    const relation = relations.find((r: any) => r.id === id);

    if (!relation) {
      throw new Error(`Relation with id ${id} not found`);
    }

    return relation;
  }

  /**
   * 更新关系信息 - 注意：当前API不支持直接更新关系
   */
  async updateGraphRelation(id: string, data: GraphRelationUpdateRequest): Promise<{ message: string; relation: Mem0GraphRelation }> {
    throw new Error('Direct relation update not supported. Relations are updated through memory updates.');
  }

  /**
   * 删除关系 - 注意：当前API不支持直接删除关系
   */
  async deleteGraphRelation(id: string): Promise<{ message: string }> {
    throw new Error('Direct relation deletion not supported. Relations are deleted through memory deletion.');
  }

  // ========================================================================
  // Graph Memory 批量操作方法
  // ========================================================================

  /**
   * 批量更新实体 - 注意：当前API不支持批量操作
   */
  async batchUpdateGraphEntities(data: GraphBatchUpdateRequest): Promise<GraphBatchOperationResponse> {
    throw new Error('Batch entity operations not supported by current API.');
  }

  /**
   * 批量删除实体 - 注意：当前API不支持批量操作
   */
  async batchDeleteGraphEntities(entityIds: string[]): Promise<GraphBatchOperationResponse> {
    throw new Error('Batch entity operations not supported by current API.');
  }

  /**
   * 批量更新关系 - 注意：当前API不支持批量操作
   */
  async batchUpdateGraphRelations(data: GraphBatchUpdateRequest): Promise<GraphBatchOperationResponse> {
    throw new Error('Batch relation operations not supported by current API.');
  }

  /**
   * 批量删除关系 - 注意：当前API不支持批量操作
   */
  async batchDeleteGraphRelations(relationIds: string[]): Promise<GraphBatchOperationResponse> {
    throw new Error('Batch relation operations not supported by current API.');
  }

  /**
   * 批量删除图元素（实体和关系）
   */
  async batchDeleteGraphElements(params: {
    entity_ids?: string[];
    relation_ids?: string[];
  }): Promise<{
    entities_result?: GraphBatchOperationResponse;
    relations_result?: GraphBatchOperationResponse;
  }> {
    const results: any = {};

    if (params.entity_ids && params.entity_ids.length > 0) {
      results.entities_result = await this.batchDeleteGraphEntities(params.entity_ids);
    }

    if (params.relation_ids && params.relation_ids.length > 0) {
      results.relations_result = await this.batchDeleteGraphRelations(params.relation_ids);
    }

    return results;
  }
}

// 创建默认客户端实例
export const createRealMem0Client = (config?: Partial<Mem0ClientConfig>): RealMem0APIClient => {
  const defaultConfig: Mem0ClientConfig = {
    baseUrl: process.env.NEXT_PUBLIC_MEM0_API_URL || process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000',
    ...config
  };
  
  return new RealMem0APIClient(defaultConfig);
};

// 默认客户端实例
export const realMem0Client = createRealMem0Client();

// 向后兼容的导出
export { RealMem0APIClient as SimplifiedMem0APIClient };
export { realMem0Client as mem0Client };
export { createRealMem0Client as createMem0Client };
