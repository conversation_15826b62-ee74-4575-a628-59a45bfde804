/**
 * SimplifiedMem0APIClient - 统一的Mem0 Server API客户端
 * 专注于Mem0核心记忆管理功能，移除配置管理和MCP相关API
 */

import axios, { AxiosInstance, AxiosResponse } from 'axios';
import {
  Mem0Memory,
  MemoryCreateRequest,
  UpdateMemoryRequest,
  SearchRequest,
  BatchUpdateRequest,
  BatchDeleteRequest,
  HTTPValidationError,
  Mem0ClientConfig,
  HealthCheckResponse
} from '@/types/mem0-api';

export class SimplifiedMem0APIClient {
  private client: AxiosInstance;
  private config: Mem0ClientConfig;

  constructor(config: Mem0ClientConfig) {
    this.config = {
      timeout: 30000,
      retries: 3,
      ...config
    };

    this.client = axios.create({
      baseURL: this.config.baseUrl,
      timeout: this.config.timeout || 30000,
      headers: {
        'Content-Type': 'application/json',
        ...(this.config.apiKey && { 'Authorization': `Bearer ${this.config.apiKey}` })
      }
    });

    // 添加响应拦截器处理错误
    this.client.interceptors.response.use(
      (response) => response,
      (error) => {
        if (error.response?.data?.detail) {
          // 处理FastAPI验证错误格式
          const validationError: HTTPValidationError = error.response.data;
          const errorMessage = validationError.detail
            .map(err => `${err.loc.join('.')}: ${err.msg}`)
            .join(', ');
          throw new Error(`Validation Error: ${errorMessage}`);
        }
        if (error.response?.data?.message) {
          throw new Error(error.response.data.message);
        }
        throw new Error(error.message || 'Network Error');
      }
    );
  }

  // 记忆管理方法
  async createMemory(request: MemoryCreateRequest): Promise<Mem0Memory> {
    const response: AxiosResponse<Mem0Memory> = await this.client.post('/v1/memories/', request);
    return response.data;
  }

  async getMemory(memoryId: string, userId: string): Promise<Mem0Memory> {
    const response: AxiosResponse<Mem0Memory> = await this.client.get(
      `/v1/memories/${memoryId}/?user_id=${userId}`
    );
    return response.data;
  }

  async updateMemory(memoryId: string, request: UpdateMemoryRequest): Promise<Mem0Memory> {
    const response: AxiosResponse<Mem0Memory> = await this.client.put(
      `/v1/memories/${memoryId}/`,
      request
    );
    return response.data;
  }

  async deleteMemory(memoryId: string, userId: string): Promise<void> {
    await this.client.delete(`/v1/memories/${memoryId}/?user_id=${userId}`);
  }

  async searchMemories(request: SearchRequest): Promise<any> {
    const response: AxiosResponse<any> = await this.client.post(
      '/v1/memories/search/',
      request
    );
    return response.data;
  }

  async getMemories(userId: string, limit = 10, offset = 0): Promise<any> {
    const response: AxiosResponse<any> = await this.client.get(
      `/v1/memories/?user_id=${userId}&limit=${limit}&offset=${offset}`
    );
    return response.data;
  }

  // 批量操作
  async batchOperation(operation: BatchUpdateRequest | BatchDeleteRequest): Promise<any> {
    const response: AxiosResponse<any> = await this.client.post(
      '/v1/batch/',
      operation
    );
    return response.data;
  }

  // 统计数据
  async getStats(userId?: string): Promise<any> {
    const url = userId ? `/v1/stats?user_id=${userId}` : '/v1/stats';
    const response: AxiosResponse<any> = await this.client.get(url);
    return response.data;
  }

  // 获取记忆历史（基于记忆ID）
  async getMemoryHistory(memoryId: string): Promise<any[]> {
    const response = await this.client.get(`/v1/memories/${memoryId}/history/`);
    return response.data;
  }

  // 获取所有可用的自定义分类
  async getAvailableCategories(userId?: string): Promise<string[]> {
    const memories = await this.getMemories(userId || 'default');
    const allCategories = memories.memories?.flatMap((m: any) => m.custom_categories || []) || [];
    return [...new Set(allCategories as string[])];
  }

  // 按分类筛选记忆
  async getMemoriesByCategory(category: string, userId?: string): Promise<any> {
    const memories = await this.getMemories(userId || 'default');
    const filtered = memories.memories?.filter((m: any) =>
      m.custom_categories?.includes(category)
    ) || [];

    return {
      memories: filtered,
      total: filtered.length,
      page: 1,
      page_size: filtered.length
    };
  }

  // 用户管理
  async getUser(userId: string): Promise<any> {
    // 注意：用户API在实际Mem0 Server中不存在，返回模拟数据
    console.warn('User API is not available in Mem0 Server - returning mock data');
    return {
      id: userId,
      name: `User ${userId}`,
      email: `${userId}@example.com`,
      created_at: new Date().toISOString(),
      is_active: true,
      metadata: {}
    };
  }

  // 用户管理（基于user_id参数）
  async getUsers(): Promise<string[]> {
    // 从现有记忆中提取所有unique的user_id
    const response = await this.client.get('/v1/memories/?limit=1000');
    const memories = response.data.memories || [];
    const userIds = memories.map((m: any) => m.user_id).filter(Boolean);
    return [...new Set(userIds as string[])];
  }

  // APP/运行实例管理（基于run_id参数）
  async getApps(userId?: string): Promise<string[]> {
    // 从现有记忆中提取所有unique的run_id
    const url = userId ? `/v1/memories/?user_id=${userId}&limit=1000` : '/v1/memories/?limit=1000';
    const response = await this.client.get(url);
    const memories = response.data.memories || [];
    const runIds = memories.map((m: any) => m.run_id).filter(Boolean);
    return [...new Set(runIds as string[])];
  }

  // 智能体管理（基于agent_id参数）
  async getAgents(userId?: string): Promise<string[]> {
    // 从现有记忆中提取所有unique的agent_id
    const url = userId ? `/v1/memories/?user_id=${userId}&limit=1000` : '/v1/memories/?limit=1000';
    const response = await this.client.get(url);
    const memories = response.data.memories || [];
    const agentIds = memories.map((m: any) => m.agent_id).filter(Boolean);
    return [...new Set(agentIds as string[])];
  }

  // 健康检查
  async healthCheck(): Promise<{ status: string; timestamp: string }> {
    const response = await this.client.get('/health');
    return response.data;
  }
}

// 创建默认客户端实例
export const createMem0Client = (config?: Partial<Mem0ClientConfig>): SimplifiedMem0APIClient => {
  const defaultConfig: Mem0ClientConfig = {
    baseUrl: process.env.NEXT_PUBLIC_MEM0_API_URL || process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8765/api/v1',
    ...config
  };
  
  return new SimplifiedMem0APIClient(defaultConfig);
};

// 默认客户端实例
export const mem0Client = createMem0Client();

// 导出realClient以保持兼容性
export { realMem0Client, RealMem0APIClient, createRealMem0Client } from './realClient';
