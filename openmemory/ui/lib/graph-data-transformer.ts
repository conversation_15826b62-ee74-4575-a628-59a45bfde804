/**
 * Graph Memory 数据转换工具
 * 实现 Mem0 API 格式与 React Flow 格式之间的双向转换
 */

import {
  Mem0GraphEntity,
  Mem0GraphRelation,
  Mem0GraphMemoryResponse
} from '@/types/mem0-api'
import {
  GraphNode,
  GraphEdge,
  GraphNodeData,
  GraphEdgeData
} from '@/types/graph-memory'

// ============================================================================
// 布局算法相关类型和常量
// ============================================================================

export interface LayoutPosition {
  x: number
  y: number
}

export interface LayoutConfig {
  width: number
  height: number
  nodeSpacing: number
  levelSpacing: number
}

// 默认布局配置
const DEFAULT_LAYOUT_CONFIG: LayoutConfig = {
  width: 800,
  height: 600,
  nodeSpacing: 150,
  levelSpacing: 200
}

// 实体类型到颜色的映射
const ENTITY_TYPE_COLORS: Record<string, string> = {
  person: '#3b82f6',      // 蓝色
  organization: '#10b981', // 绿色
  project: '#f59e0b',     // 橙色
  location: '#ef4444',    // 红色
  concept: '#8b5cf6',     // 紫色
  event: '#06b6d4',       // 青色
  document: '#84cc16',    // 绿黄色
  default: '#6b7280'      // 灰色
}

// 关系类型到样式的映射
const RELATION_TYPE_STYLES: Record<string, { color: string; style: 'solid' | 'dashed' | 'dotted' }> = {
  works_for: { color: '#3b82f6', style: 'solid' },
  contributes_to: { color: '#10b981', style: 'solid' },
  located_in: { color: '#ef4444', style: 'solid' },
  related_to: { color: '#6b7280', style: 'dashed' },
  depends_on: { color: '#f59e0b', style: 'dotted' },
  default: { color: '#6b7280', style: 'solid' }
}

// ============================================================================
// Mem0 API 格式 → React Flow 格式转换
// ============================================================================

/**
 * 将 Mem0 实体转换为 React Flow 节点
 */
export function transformEntityToNode(entity: Mem0GraphEntity, position?: LayoutPosition): GraphNode {
  const nodeData: GraphNodeData = {
    id: entity.id,
    label: entity.name,
    type: entity.type,
    description: entity.description,
    properties: entity.properties,
    metadata: entity.metadata,
    // 视觉属性
    color: ENTITY_TYPE_COLORS[entity.type] || ENTITY_TYPE_COLORS.default,
    size: 1,
    icon: getEntityIcon(entity.type)
  }

  return {
    id: entity.id,
    type: 'default',
    position: position || { x: Math.random() * 500, y: Math.random() * 500 },
    data: nodeData
  }
}

/**
 * 将 Mem0 关系转换为 React Flow 边
 */
export function transformRelationToEdge(relation: Mem0GraphRelation): GraphEdge {
  const edgeData: GraphEdgeData = {
    id: relation.id,
    label: relation.relation_type,
    relation_type: relation.relation_type,
    description: relation.description,
    weight: relation.weight,
    properties: relation.properties,
    metadata: relation.metadata,
    // 视觉属性
    color: RELATION_TYPE_STYLES[relation.relation_type]?.color || RELATION_TYPE_STYLES.default.color,
    width: Math.max(1, (relation.weight || 0.5) * 3), // 根据权重调整线条粗细
    style: RELATION_TYPE_STYLES[relation.relation_type]?.style || RELATION_TYPE_STYLES.default.style
  }

  return {
    id: relation.id,
    source: relation.source_entity_id,
    target: relation.target_entity_id,
    type: 'default',
    data: edgeData,
    animated: (relation.weight || 0) > 0.8, // 高权重关系显示动画
    style: {
      stroke: edgeData.color,
      strokeWidth: edgeData.width
    }
  }
}

/**
 * 将完整的 Mem0 Graph Memory 响应转换为 React Flow 格式
 */
export function transformMem0GraphToReactFlow(
  graphData: Mem0GraphMemoryResponse,
  layoutType: 'force' | 'hierarchical' | 'circular' | 'grid' = 'force',
  layoutConfig: Partial<LayoutConfig> = {}
): { nodes: GraphNode[]; edges: GraphEdge[] } {
  const config = { ...DEFAULT_LAYOUT_CONFIG, ...layoutConfig }
  
  // 转换实体为节点
  const nodes = graphData.entities.map((entity, index) => {
    const position = calculateNodePosition(index, graphData.entities.length, layoutType, config)
    return transformEntityToNode(entity, position)
  })

  // 转换关系为边
  const edges = graphData.relations.map(relation => transformRelationToEdge(relation))

  return { nodes, edges }
}

// ============================================================================
// React Flow 格式 → Mem0 API 格式转换
// ============================================================================

/**
 * 将 React Flow 节点转换为 Mem0 实体
 */
export function transformNodeToEntity(node: GraphNode): Mem0GraphEntity {
  return {
    id: node.data.id,
    name: node.data.label,
    type: node.data.type,
    description: node.data.description,
    properties: node.data.properties,
    metadata: {
      ...node.data.metadata,
      // 保存位置信息
      position: node.position,
      visual_properties: {
        color: node.data.color,
        size: node.data.size,
        icon: node.data.icon
      }
    }
  }
}

/**
 * 将 React Flow 边转换为 Mem0 关系
 */
export function transformEdgeToRelation(edge: GraphEdge): Mem0GraphRelation {
  const edgeData = edge.data as GraphEdgeData | undefined

  return {
    id: edgeData?.id || edge.id,
    source_entity_id: edge.source,
    target_entity_id: edge.target,
    relation_type: edgeData?.relation_type || 'related_to',
    description: edgeData?.description,
    weight: edgeData?.weight,
    properties: edgeData?.properties,
    metadata: {
      ...(edgeData?.metadata || {}),
      // 保存视觉属性
      visual_properties: {
        color: edgeData?.color,
        width: edgeData?.width,
        style: edgeData?.style
      }
    }
  }
}

/**
 * 将 React Flow 格式转换为 Mem0 Graph Memory 格式
 */
export function transformReactFlowToMem0Graph(
  nodes: GraphNode[],
  edges: GraphEdge[]
): Mem0GraphMemoryResponse {
  const entities = nodes.map(transformNodeToEntity)
  const relations = edges.map(transformEdgeToRelation)

  return {
    entities,
    relations,
    metadata: {
      total_entities: entities.length,
      total_relations: relations.length,
      graph_density: calculateGraphDensity(entities.length, relations.length),
      active_entities: entities.length
    }
  }
}

// ============================================================================
// 布局算法实现
// ============================================================================

/**
 * 根据布局类型计算节点位置
 */
function calculateNodePosition(
  index: number,
  totalNodes: number,
  layoutType: 'force' | 'hierarchical' | 'circular' | 'grid',
  config: LayoutConfig
): LayoutPosition {
  switch (layoutType) {
    case 'circular':
      return calculateCircularPosition(index, totalNodes, config)
    case 'grid':
      return calculateGridPosition(index, totalNodes, config)
    case 'hierarchical':
      return calculateHierarchicalPosition(index, totalNodes, config)
    case 'force':
    default:
      return calculateForcePosition(index, totalNodes, config)
  }
}

/**
 * 圆形布局
 */
function calculateCircularPosition(index: number, totalNodes: number, config: LayoutConfig): LayoutPosition {
  const angle = (2 * Math.PI * index) / totalNodes
  const radius = Math.min(config.width, config.height) / 3
  const centerX = config.width / 2
  const centerY = config.height / 2

  return {
    x: centerX + radius * Math.cos(angle),
    y: centerY + radius * Math.sin(angle)
  }
}

/**
 * 网格布局
 */
function calculateGridPosition(index: number, totalNodes: number, config: LayoutConfig): LayoutPosition {
  const cols = Math.ceil(Math.sqrt(totalNodes))
  const rows = Math.ceil(totalNodes / cols)
  
  const col = index % cols
  const row = Math.floor(index / cols)
  
  const cellWidth = config.width / cols
  const cellHeight = config.height / rows

  return {
    x: col * cellWidth + cellWidth / 2,
    y: row * cellHeight + cellHeight / 2
  }
}

/**
 * 层次布局
 */
function calculateHierarchicalPosition(index: number, totalNodes: number, config: LayoutConfig): LayoutPosition {
  const levels = Math.ceil(Math.sqrt(totalNodes))
  const nodesPerLevel = Math.ceil(totalNodes / levels)
  
  const level = Math.floor(index / nodesPerLevel)
  const positionInLevel = index % nodesPerLevel
  
  const levelWidth = config.width / nodesPerLevel
  const levelHeight = config.height / levels

  return {
    x: positionInLevel * levelWidth + levelWidth / 2,
    y: level * levelHeight + levelHeight / 2
  }
}

/**
 * 力导向布局（随机初始位置）
 */
function calculateForcePosition(index: number, totalNodes: number, config: LayoutConfig): LayoutPosition {
  return {
    x: Math.random() * config.width,
    y: Math.random() * config.height
  }
}

// ============================================================================
// 辅助函数
// ============================================================================

/**
 * 根据实体类型获取图标
 */
function getEntityIcon(entityType: string): string {
  const iconMap: Record<string, string> = {
    person: '👤',
    organization: '🏢',
    project: '📁',
    location: '📍',
    concept: '💡',
    event: '📅',
    document: '📄'
  }
  return iconMap[entityType] || '⚪'
}

/**
 * 计算图密度
 */
function calculateGraphDensity(nodeCount: number, edgeCount: number): number {
  if (nodeCount <= 1) return 0
  const maxPossibleEdges = nodeCount * (nodeCount - 1) / 2
  return edgeCount / maxPossibleEdges
}

/**
 * 验证图数据的完整性
 */
export function validateGraphData(nodes: GraphNode[], edges: GraphEdge[]): {
  isValid: boolean
  errors: string[]
} {
  const errors: string[] = []
  const nodeIds = new Set(nodes.map(node => node.id))

  // 检查边的源和目标节点是否存在
  edges.forEach(edge => {
    if (!nodeIds.has(edge.source)) {
      errors.push(`Edge ${edge.id} references non-existent source node: ${edge.source}`)
    }
    if (!nodeIds.has(edge.target)) {
      errors.push(`Edge ${edge.id} references non-existent target node: ${edge.target}`)
    }
  })

  // 检查节点ID的唯一性
  const duplicateNodeIds = nodes
    .map(node => node.id)
    .filter((id, index, arr) => arr.indexOf(id) !== index)
  
  if (duplicateNodeIds.length > 0) {
    errors.push(`Duplicate node IDs found: ${duplicateNodeIds.join(', ')}`)
  }

  // 检查边ID的唯一性
  const duplicateEdgeIds = edges
    .map(edge => edge.id)
    .filter((id, index, arr) => arr.indexOf(id) !== index)
  
  if (duplicateEdgeIds.length > 0) {
    errors.push(`Duplicate edge IDs found: ${duplicateEdgeIds.join(', ')}`)
  }

  return {
    isValid: errors.length === 0,
    errors
  }
}
