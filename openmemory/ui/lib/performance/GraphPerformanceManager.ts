/**
 * Graph Memory 性能管理器
 * 
 * 负责图可视化的性能优化，包括：
 * - 设备性能评估
 * - 渲染策略选择
 * - LOD（Level of Detail）管理
 * - 内存监控
 * - 性能指标收集
 */

import { GraphNode, GraphEdge } from '@/types/graph-memory';

// 设备性能评估结果
export interface DeviceCapability {
  score: number; // 0-100
  memory: number; // GB
  cores: number;
  gpu: boolean;
  mobile: boolean;
  category: 'low' | 'medium' | 'high';
}

// 渲染策略配置
export interface RenderingStrategy {
  name: string;
  maxNodes: number;
  maxEdges: number;
  enableLOD: boolean;
  enableVirtualization: boolean;
  enableHardwareAcceleration: boolean;
  nodeSimplificationThreshold: number;
  edgeSimplificationThreshold: number;
}

// 性能指标
export interface PerformanceMetrics {
  renderTime: number;
  frameRate: number;
  memoryUsage: number;
  nodeCount: number;
  edgeCount: number;
  timestamp: number;
}

// LOD 级别定义
export enum LODLevel {
  MINIMAL = 0,    // 最简化显示
  LOW = 1,        // 低细节
  MEDIUM = 2,     // 中等细节
  HIGH = 3        // 高细节
}

class GraphPerformanceManager {
  private static instance: GraphPerformanceManager;
  private deviceCapability: DeviceCapability | null = null;
  private currentStrategy: RenderingStrategy | null = null;
  private performanceMetrics: PerformanceMetrics[] = [];
  private memoryObserver: PerformanceObserver | null = null;

  private constructor() {
    // 只在客户端初始化性能监控
    if (typeof window !== 'undefined') {
      this.initializePerformanceMonitoring();
    }
  }

  public static getInstance(): GraphPerformanceManager {
    if (!GraphPerformanceManager.instance) {
      GraphPerformanceManager.instance = new GraphPerformanceManager();
    }
    return GraphPerformanceManager.instance;
  }

  /**
   * 评估设备性能能力
   */
  public async assessDeviceCapability(): Promise<DeviceCapability> {
    if (this.deviceCapability) {
      return this.deviceCapability;
    }

    const capability: DeviceCapability = {
      score: 50,
      memory: 4,
      cores: 4,
      gpu: false,
      mobile: false,
      category: 'medium'
    };

    try {
      // 只在客户端环境中进行检测
      if (typeof window === 'undefined') {
        this.deviceCapability = capability;
        return capability;
      }

      // 检测内存
      if (typeof performance !== 'undefined' && 'memory' in performance) {
        const memInfo = (performance as any).memory;
        capability.memory = Math.round(memInfo.jsHeapSizeLimit / (1024 * 1024 * 1024));
      }

      // 检测CPU核心数
      if (typeof navigator !== 'undefined' && 'hardwareConcurrency' in navigator) {
        capability.cores = navigator.hardwareConcurrency || 4;
      }

      // 检测GPU支持
      if (typeof document !== 'undefined') {
        const canvas = document.createElement('canvas');
        const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
        capability.gpu = !!gl;
      }

      // 检测移动设备
      if (typeof navigator !== 'undefined') {
        capability.mobile = /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
      }

      // 计算综合评分
      let score = 0;
      score += Math.min(capability.memory * 10, 40); // 内存权重40%
      score += Math.min(capability.cores * 5, 20);   // CPU权重20%
      score += capability.gpu ? 20 : 0;              // GPU权重20%
      score += capability.mobile ? 0 : 20;           // 桌面设备加分20%

      capability.score = Math.min(score, 100);

      // 确定设备类别
      if (capability.score >= 80) {
        capability.category = 'high';
      } else if (capability.score >= 50) {
        capability.category = 'medium';
      } else {
        capability.category = 'low';
      }

    } catch (error) {
      console.warn('Failed to assess device capability:', error);
    }

    this.deviceCapability = capability;
    return capability;
  }

  /**
   * 选择最适合的渲染策略
   */
  public async selectRenderingStrategy(): Promise<RenderingStrategy> {
    const capability = await this.assessDeviceCapability();

    const strategies: Record<string, RenderingStrategy> = {
      high: {
        name: 'High Performance',
        maxNodes: 2000,
        maxEdges: 5000,
        enableLOD: true,
        enableVirtualization: false,
        enableHardwareAcceleration: true,
        nodeSimplificationThreshold: 1000,
        edgeSimplificationThreshold: 2000
      },
      medium: {
        name: 'Balanced Performance',
        maxNodes: 500,
        maxEdges: 1000,
        enableLOD: true,
        enableVirtualization: true,
        enableHardwareAcceleration: true,
        nodeSimplificationThreshold: 200,
        edgeSimplificationThreshold: 400
      },
      low: {
        name: 'Low Performance',
        maxNodes: 100,
        maxEdges: 200,
        enableLOD: true,
        enableVirtualization: true,
        enableHardwareAcceleration: false,
        nodeSimplificationThreshold: 50,
        edgeSimplificationThreshold: 100
      }
    };

    this.currentStrategy = strategies[capability.category];
    return this.currentStrategy;
  }

  /**
   * 计算当前视图的LOD级别
   */
  public calculateLODLevel(
    nodeCount: number, 
    edgeCount: number, 
    zoomLevel: number
  ): LODLevel {
    if (!this.currentStrategy) {
      return LODLevel.MEDIUM;
    }

    // 基于节点数量的LOD
    let lodByCount = LODLevel.HIGH;
    if (nodeCount > this.currentStrategy.nodeSimplificationThreshold) {
      lodByCount = LODLevel.LOW;
    } else if (nodeCount > this.currentStrategy.nodeSimplificationThreshold / 2) {
      lodByCount = LODLevel.MEDIUM;
    }

    // 基于缩放级别的LOD
    let lodByZoom = LODLevel.HIGH;
    if (zoomLevel < 0.5) {
      lodByZoom = LODLevel.MINIMAL;
    } else if (zoomLevel < 1.0) {
      lodByZoom = LODLevel.LOW;
    } else if (zoomLevel < 2.0) {
      lodByZoom = LODLevel.MEDIUM;
    }

    // 返回更保守的LOD级别
    return Math.min(lodByCount, lodByZoom) as LODLevel;
  }

  /**
   * 优化节点数据以提升渲染性能
   */
  public optimizeNodesForRendering(
    nodes: GraphNode[], 
    lodLevel: LODLevel
  ): GraphNode[] {
    return nodes.map(node => {
      const optimizedNode = { ...node };

      switch (lodLevel) {
        case LODLevel.MINIMAL:
          // 最简化：只保留基本信息
          optimizedNode.data = {
            ...node.data,
            description: undefined,
            properties: undefined
          };
          break;

        case LODLevel.LOW:
          // 低细节：简化描述
          if (node.data.description && node.data.description.length > 50) {
            optimizedNode.data.description = node.data.description.substring(0, 47) + '...';
          }
          break;

        case LODLevel.MEDIUM:
          // 中等细节：适度简化
          if (node.data.description && node.data.description.length > 100) {
            optimizedNode.data.description = node.data.description.substring(0, 97) + '...';
          }
          break;

        case LODLevel.HIGH:
        default:
          // 高细节：保持原样
          break;
      }

      return optimizedNode;
    });
  }

  /**
   * 优化边数据以提升渲染性能
   */
  public optimizeEdgesForRendering(
    edges: GraphEdge[], 
    lodLevel: LODLevel
  ): GraphEdge[] {
    if (lodLevel === LODLevel.MINIMAL) {
      // 最简化：只显示权重最高的边
      return edges
        .sort((a, b) => (b.data?.weight || 0) - (a.data?.weight || 0))
        .slice(0, Math.min(edges.length, 50));
    }

    return edges.map(edge => {
      const optimizedEdge = { ...edge };

      if (lodLevel <= LODLevel.LOW) {
        // 低细节：移除标签
        optimizedEdge.label = undefined;
        optimizedEdge.labelStyle = undefined;
      }

      return optimizedEdge;
    });
  }

  /**
   * 记录性能指标
   */
  public recordPerformanceMetrics(
    renderTime: number,
    nodeCount: number,
    edgeCount: number
  ): void {
    const metrics: PerformanceMetrics = {
      renderTime,
      frameRate: this.getCurrentFrameRate(),
      memoryUsage: this.getCurrentMemoryUsage(),
      nodeCount,
      edgeCount,
      timestamp: Date.now()
    };

    this.performanceMetrics.push(metrics);

    // 只保留最近100条记录
    if (this.performanceMetrics.length > 100) {
      this.performanceMetrics = this.performanceMetrics.slice(-100);
    }
  }

  /**
   * 获取性能统计信息
   */
  public getPerformanceStats() {
    if (this.performanceMetrics.length === 0) {
      return null;
    }

    const recent = this.performanceMetrics.slice(-10);
    const avgRenderTime = recent.reduce((sum, m) => sum + m.renderTime, 0) / recent.length;
    const avgFrameRate = recent.reduce((sum, m) => sum + m.frameRate, 0) / recent.length;
    const avgMemoryUsage = recent.reduce((sum, m) => sum + m.memoryUsage, 0) / recent.length;

    return {
      averageRenderTime: Math.round(avgRenderTime),
      averageFrameRate: Math.round(avgFrameRate),
      averageMemoryUsage: Math.round(avgMemoryUsage),
      totalMetrics: this.performanceMetrics.length,
      deviceCapability: this.deviceCapability,
      currentStrategy: this.currentStrategy
    };
  }

  /**
   * 初始化性能监控
   */
  private initializePerformanceMonitoring(): void {
    if (typeof window !== 'undefined' && 'PerformanceObserver' in window) {
      try {
        this.memoryObserver = new PerformanceObserver((list) => {
          // 处理性能条目
        });
        this.memoryObserver.observe({ entryTypes: ['measure'] });
      } catch (error) {
        console.warn('Failed to initialize performance monitoring:', error);
      }
    }
  }

  /**
   * 获取当前帧率
   */
  private getCurrentFrameRate(): number {
    // 简化的帧率计算
    return 60; // 默认值，实际应用中可以实现更精确的计算
  }

  /**
   * 获取当前内存使用量（MB）
   */
  private getCurrentMemoryUsage(): number {
    if (typeof performance !== 'undefined' && 'memory' in performance) {
      const memInfo = (performance as any).memory;
      return Math.round(memInfo.usedJSHeapSize / (1024 * 1024));
    }
    return 0;
  }

  /**
   * 清理资源
   */
  public cleanup(): void {
    if (this.memoryObserver) {
      this.memoryObserver.disconnect();
      this.memoryObserver = null;
    }
    this.performanceMetrics = [];
  }
}

export default GraphPerformanceManager;
