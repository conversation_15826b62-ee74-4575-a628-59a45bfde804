#!/bin/bash

# 启动OpenMemory UI部分的Docker容器
# 使用方法: ./start-ui-only.sh

set -e

echo "🚀 启动OpenMemory UI部分..."

# 检查Docker是否运行
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker未运行，请先启动Docker服务"
    exit 1
fi

# 设置默认环境变量
export NEXT_PUBLIC_API_URL=${NEXT_PUBLIC_API_URL:-http://localhost:8765}
export USER=${USER:-default}

echo "📋 环境变量配置:"
echo "   NEXT_PUBLIC_API_URL: $NEXT_PUBLIC_API_URL"
echo "   USER: $USER"

# 停止并删除现有容器（如果存在）
echo "🔄 清理现有容器..."
docker compose -f docker-compose-ui-only.yml down --remove-orphans 2>/dev/null || true

# 构建并启动UI容器
echo "🔨 构建UI镜像..."
docker compose -f docker-compose-ui-only.yml build

echo "🚀 启动UI服务..."
docker compose -f docker-compose-ui-only.yml up -d

# 等待服务启动
echo "⏳ 等待服务启动..."
sleep 5

# 检查服务状态
if docker compose -f docker-compose-ui-only.yml ps | grep -q "Up"; then
    echo "✅ UI服务启动成功！"
    echo "🌐 访问地址: http://localhost:3000"
    echo ""
    echo "📋 服务状态:"
    docker compose -f docker-compose-ui-only.yml ps
else
    echo "❌ UI服务启动失败"
    echo "📋 查看日志:"
    docker compose -f docker-compose-ui-only.yml logs
    exit 1
fi 