# OpenMemory UI API端点支持文档

## 1. 文档概述

本文档描述了为支持OpenMemory UI界面开发所需的API端点规范。基于现有Mem0 Server架构，补充UI管理功能所需的关键端点。

## 2. 现有API端点分析

### 2.1 已实现的核心端点
```
✅ POST /v1/memories/           - 创建记忆
✅ GET  /v1/memories/           - 获取记忆列表
✅ GET  /v1/memories/{id}/      - 获取单个记忆
✅ PUT  /v1/memories/{id}/      - 更新记忆
✅ DELETE /v1/memories/{id}/    - 删除记忆
✅ DELETE /v1/memories/         - 删除所有记忆
✅ POST /v1/memories/search/    - 搜索记忆
✅ PUT  /v1/batch/              - 批量更新记忆
✅ DELETE /v1/batch/            - 批量删除记忆
✅ POST /v1/exports/            - 记忆导出
✅ POST /v1/feedback/           - 提交反馈
✅ GET  /health                 - 健康检查
✅ GET  /cache/status           - 缓存状态
✅ POST /cache/clear            - 清除缓存
```

### 2.2 缺失的UI管理端点
```
❌ /v1/stats                   - 系统统计数据
❌ /v1/activities              - 操作活动日志
❌ /v1/users                   - 用户管理
❌ /v1/graph/entities          - 图实体管理
❌ /v1/graph/relationships     - 图关系管理
❌ /v1/admin/dashboard         - 统一管理面板数据
```

## 3. 新增API端点规范

### 3.1 统计数据API

#### GET /v1/stats
**用途**：为Dashboard统计面板提供系统统计数据

**查询参数**：
```typescript
interface StatsQueryParams {
  user_id?: string;           // 可选，获取特定用户统计
  time_range?: '1h' | '24h' | '7d' | '30d';  // 时间范围，默认24h
}
```

**响应格式**：
```typescript
interface StatsResponse {
  // 基础统计（替换后的指标）
  total_memories: number;      // 总记忆数
  total_users: number;         // 总用户数  
  search_events: number;       // 检索事件数量（替换平均响应时间）
  add_events: number;          // 添加事件数量（替换活跃用户数）
  
  // Graph Memory统计
  graph_memories: number;      // 图记忆数量
  entities_count: number;      // 实体总数
  relationships_count: number; // 关系总数
  graph_density: number;       // 图密度 (0-1)
  
  // 时间统计
  last_updated: string;        // 最后更新时间
  time_range: string;          // 统计时间范围
}
```

**实现示例**：
```python
@app.get("/v1/stats", summary="Get system statistics")
async def get_stats(
    user_id: Optional[str] = Query(None, description="User ID for user-specific stats"),
    time_range: str = Query("24h", regex="^(1h|24h|7d|30d)$", description="Time range for statistics")
):
    try:
        # 计算基础统计
        if user_id:
            total_memories = await count_user_memories(user_id)
            total_users = 1
        else:
            total_memories = await count_all_memories()
            total_users = await count_unique_users()
        
        # 计算事件统计（从活动日志或记忆历史中统计）
        search_events = await count_search_events(user_id, time_range)
        add_events = await count_add_events(user_id, time_range)
        
        # Graph Memory统计
        graph_stats = await get_graph_statistics(user_id)
        
        return {
            "total_memories": total_memories,
            "total_users": total_users,
            "search_events": search_events,
            "add_events": add_events,
            "graph_memories": graph_stats.get("graph_count", 0),
            "entities_count": graph_stats.get("entities", 0),
            "relationships_count": graph_stats.get("relationships", 0),
            "graph_density": graph_stats.get("density", 0.0),
            "last_updated": datetime.now().isoformat(),
            "time_range": time_range
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
```

### 3.2 活动日志API

#### GET /v1/activities
**用途**：为Activity时间线组件提供操作历史记录

**查询参数**：
```typescript
interface ActivitiesQueryParams {
  user_id?: string;                    // 可选，筛选特定用户
  limit?: number;                      // 返回数量限制，默认50，最大200
  offset?: number;                     // 分页偏移，默认0
  operation_type?: 'SEARCH' | 'ADD' | 'UPDATE' | 'DELETE' | 'GRAPH_CREATE'; // 操作类型筛选
  start_time?: string;                 // 开始时间 (ISO格式)
  end_time?: string;                   // 结束时间 (ISO格式)
}
```

**响应格式**：
```typescript
interface Activity {
  id: string;                          // 活动记录ID
  timestamp: string;                   // 操作时间戳 (ISO格式)
  operation: 'SEARCH' | 'ADD' | 'UPDATE' | 'DELETE' | 'GRAPH_CREATE';
  details: string;                     // 操作详情描述
  response_time?: string;              // 响应时间（如"45ms"）
  status: 'success' | 'error' | 'pending';
  user_id?: string;                    // 用户ID
  memory_id?: string;                  // 相关记忆ID
  metadata?: Record<string, any>;      // 额外元数据
}

interface ActivitiesResponse {
  activities: Activity[];
  total: number;                       // 总记录数
  has_more: boolean;                   // 是否有更多数据
  time_range: {
    start: string;
    end: string;
  };
}
```

**实现示例**：
```python
@app.get("/v1/activities", summary="Get activity logs")
async def get_activities(
    user_id: Optional[str] = Query(None),
    limit: int = Query(50, ge=1, le=200),
    offset: int = Query(0, ge=0),
    operation_type: Optional[str] = Query(None),
    start_time: Optional[str] = Query(None),
    end_time: Optional[str] = Query(None)
):
    try:
        # 构建查询条件
        filters = {}
        if user_id:
            filters['user_id'] = user_id
        if operation_type:
            filters['operation'] = operation_type
        if start_time:
            filters['start_time'] = start_time
        if end_time:
            filters['end_time'] = end_time
        
        # 查询活动日志
        activities, total = await query_activities(
            filters=filters,
            limit=limit,
            offset=offset
        )
        
        return {
            "activities": activities,
            "total": total,
            "has_more": offset + len(activities) < total,
            "time_range": {
                "start": start_time or (datetime.now() - timedelta(days=7)).isoformat(),
                "end": end_time or datetime.now().isoformat()
            }
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
```

### 3.3 用户管理API

#### GET /v1/users
**用途**：获取用户列表和基础统计信息

```python
@app.get("/v1/users", summary="Get all users with statistics")
async def get_users(
    limit: int = Query(100, ge=1, le=500),
    offset: int = Query(0, ge=0),
    include_stats: bool = Query(True, description="Include user statistics")
):
    try:
        users = await get_users_list(limit=limit, offset=offset)
        
        if include_stats:
            for user in users:
                user['stats'] = await get_user_stats(user['user_id'])
        
        total = await count_total_users()
        
        return {
            "users": users,
            "total": total,
            "has_more": offset + len(users) < total
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
```

#### GET /v1/users/{user_id}/stats
**用途**：获取特定用户的详细统计信息

```python
@app.get("/v1/users/{user_id}/stats", summary="Get user statistics")
async def get_user_stats(user_id: str):
    try:
        stats = {
            "user_id": user_id,
            "total_memories": await count_user_memories(user_id),
            "search_events": await count_user_search_events(user_id),
            "add_events": await count_user_add_events(user_id),
            "graph_memories": await count_user_graph_memories(user_id),
            "last_activity": await get_user_last_activity(user_id),
            "created_at": await get_user_created_at(user_id)
        }
        
        return stats
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
```

#### DELETE /v1/users/{user_id}
**用途**：删除用户及其所有相关数据

```python
@app.delete("/v1/users/{user_id}", summary="Delete user and all associated data")
async def delete_user(user_id: str):
    try:
        # 删除用户的所有记忆
        deleted_memories = await delete_all_user_memories(user_id)
        
        # 删除用户的活动日志
        deleted_activities = await delete_user_activities(user_id)
        
        # 删除用户的图数据
        deleted_graph_data = await delete_user_graph_data(user_id)
        
        return {
            "message": f"User {user_id} and all associated data deleted successfully",
            "deleted_memories": deleted_memories,
            "deleted_activities": deleted_activities,
            "deleted_graph_data": deleted_graph_data
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
```

### 3.4 统一管理面板API（推荐实现）

#### GET /v1/admin/dashboard
**用途**：一次性获取Dashboard所需的所有数据，减少网络请求

```python
@app.get("/v1/admin/dashboard", summary="Get comprehensive dashboard data")
async def get_dashboard_data(
    user_id: Optional[str] = Query(None),
    time_range: str = Query("24h", regex="^(1h|24h|7d|30d)$")
):
    try:
        # 并行获取所有数据
        stats_task = get_stats(user_id, time_range)
        activities_task = get_activities(user_id, limit=10)
        users_summary_task = get_users_summary()
        
        stats, recent_activities, users_summary = await asyncio.gather(
            stats_task,
            activities_task, 
            users_summary_task
        )
        
        return {
            "stats": stats,
            "recent_activities": recent_activities["activities"][:10],
            "users_summary": users_summary,
            "quick_actions": [
                {"id": "create", "label": "Create Memory", "icon": "plus"},
                {"id": "search", "label": "Search Memories", "icon": "search"},
                {"id": "graph", "label": "Graph Memory", "icon": "network"},
                {"id": "users", "label": "Manage Users", "icon": "users"}
            ],
            "last_updated": datetime.now().isoformat()
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
```

## 4. Graph Memory专用API增强

### 4.1 图实体管理

#### GET /v1/graph/entities
```python
@app.get("/v1/graph/entities", summary="Get graph entities")
async def get_graph_entities(
    user_id: Optional[str] = Query(None),
    entity_type: Optional[str] = Query(None),
    limit: int = Query(100, ge=1, le=500)
):
    # 返回图实体列表
```

#### POST /v1/graph/entities
```python
@app.post("/v1/graph/entities", summary="Create graph entity")
async def create_graph_entity(entity_data: GraphEntityCreate):
    # 创建新的图实体
```

### 4.2 图关系管理

#### GET /v1/graph/relationships
```python
@app.get("/v1/graph/relationships", summary="Get graph relationships")
async def get_graph_relationships(
    user_id: Optional[str] = Query(None),
    source_entity: Optional[str] = Query(None),
    target_entity: Optional[str] = Query(None),
    limit: int = Query(100, ge=1, le=500)
):
    # 返回图关系列表
```

#### POST /v1/graph/relationships
```python
@app.post("/v1/graph/relationships", summary="Create graph relationship")
async def create_graph_relationship(relationship_data: GraphRelationshipCreate):
    # 创建新的图关系
```

## 5. 实现优先级和开发计划

### 5.1 开发优先级

**P0 (第2周必需)**：
1. `GET /v1/stats` - Dashboard核心统计数据
2. `GET /v1/activities` - Activity时间线数据
3. `GET /v1/admin/dashboard` - 统一管理面板API（推荐）

**P1 (第2-3周重要)**：
4. `GET /v1/users` - 用户管理基础功能
5. `DELETE /v1/users/{user_id}` - 用户删除功能
6. `GET /v1/users/{user_id}/stats` - 用户统计详情

**P2 (第3周或后续)**：
7. Graph Memory专用API增强
8. 活动日志的高级筛选和搜索功能

### 5.2 快速实现建议

1. **先实现统一API**：`/v1/admin/dashboard` 可以快速支持Dashboard显示
2. **活动日志可以基于现有记忆历史**：利用现有的记忆操作历史数据
3. **用户统计可以实时计算**：基于现有记忆数据进行聚合查询

### 5.3 数据存储建议

**活动日志存储**：
- 可以扩展现有的SQLite history.db
- 或者基于现有记忆操作记录进行实时聚合

**用户统计存储**：
- 基于现有记忆数据进行实时统计
- 可考虑添加缓存机制提升性能

## 6. 前端集成示例

### 6.1 统计数据获取
```typescript
// 获取Dashboard统计数据
const dashboardData = await fetch('/v1/admin/dashboard?time_range=24h')
  .then(res => res.json());

// 统计面板显示
const StatsPanel = () => (
  <div className="stats-grid">
    <StatCard title="Total Memories" value={dashboardData.stats.total_memories} />
    <StatCard title="Total Users" value={dashboardData.stats.total_users} />
    <StatCard title="Search Events" value={dashboardData.stats.search_events} />
    <StatCard title="Add Events" value={dashboardData.stats.add_events} />
  </div>
);
```

### 6.2 Activity时间线集成
```typescript
// Activity时间线组件
const ActivityTimeline = () => {
  const activities = dashboardData.recent_activities;
  
  return (
    <div className="activity-timeline">
      {activities.map(activity => (
        <ActivityItem
          key={activity.id}
          operation={activity.operation}
          details={activity.details}
          timestamp={activity.timestamp}
          status={activity.status}
        />
      ))}
    </div>
  );
};
```

## 7. 测试和验证

### 7.1 API测试用例
```bash
# 测试统计API
curl "http://localhost:8000/v1/stats?time_range=24h"

# 测试活动日志API
curl "http://localhost:8000/v1/activities?limit=10&operation_type=ADD"

# 测试用户管理API
curl "http://localhost:8000/v1/users?limit=20&include_stats=true"

# 测试统一管理面板API
curl "http://localhost:8000/v1/admin/dashboard"
```

### 7.2 预期响应示例
```json
{
  "stats": {
    "total_memories": 156,
    "total_users": 8,
    "search_events": 34,
    "add_events": 23,
    "graph_memories": 12,
    "entities_count": 45,
    "relationships_count": 67,
    "graph_density": 0.15,
    "last_updated": "2024-07-29T12:30:00Z",
    "time_range": "24h"
  },
  "recent_activities": [
    {
      "id": "act_001",
      "timestamp": "2024-07-29T12:25:30Z",
      "operation": "ADD",
      "details": "Created memory: User prefers dark theme",
      "status": "success",
      "user_id": "user_123"
    }
  ]
}
```

这份文档涵盖了支持OpenMemory UI所需的所有关键API端点，按照开发优先级排序，并提供了完整的实现规范和集成示例。