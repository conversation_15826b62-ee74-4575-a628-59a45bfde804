# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Common Development Commands

### Python Package Management
- **Build package**: `hatch build`
- **Install all dependencies**: `make install_all`
- **Create environment**: `hatch env create`

### Code Quality
- **Format code**: `hatch run format` or `make format`
- **Lint code**: `hatch run lint` or `make lint`
- **Sort imports**: `hatch run isort mem0/` or `make sort`
- **Combined**: `make all` (format, sort, lint)

### Testing
- **Run all tests**: `hatch run test` or `make test`
- **Run tests for specific Python version**: 
  - `make test-py-3.9`
  - `make test-py-3.10`
  - `make test-py-3.11`

### Documentation
- **Run docs server**: `make docs` (requires mintlify)

### Publishing
- **Build package**: `make build`
- **Publish package**: `make publish`
- **Clean build artifacts**: `make clean`

## Core Architecture & Design Patterns

### Memory System Architecture
The Mem0 memory system follows a layered architecture with these key components:

1. **Memory Interface Layer**: `Memory` and `AsyncMemory` classes in `mem0/memory/main.py`
   - Session management through `user_id`, `agent_id`, `run_id` parameters
   - Multi-level memory scoping (User, Agent, Run) with metadata filtering
   - Advanced retrieval with optional performance monitoring

2. **Configuration System**: Pydantic-based configuration in `mem0/configs/`
   - `MemoryConfig`: Main configuration with nested component configs
   - Environment variable support with fallback defaults
   - Automatic directory creation and path validation
   - Custom instruction support via `custom_fact_extraction_prompt`

3. **Factory Pattern Implementation**: Dynamic component creation in `mem0/utils/factory.py`
   - `LlmFactory`: 34+ LLM providers (OpenAI, Anthropic, Azure, Groq, etc.)
   - `EmbedderFactory`: 9+ embedding providers with dimension validation
   - `VectorStoreFactory`: 16+ vector database integrations
   - Runtime class loading with error handling

4. **Memory Processing Pipeline**:
   - **Fact Extraction**: LLM-based extraction from conversations using prompts in `mem0/configs/prompts.py`
   - **Memory Updates**: Smart memory management (ADD, UPDATE, DELETE, NONE operations)
   - **Storage**: Vector embeddings + SQLite history + optional graph storage
   - **Retrieval**: Semantic search with metadata filtering and BM25 support

### Graph Memory Architecture
- **Neptune Integration**: AWS Neptune graph database support (`mem0/graphs/neptune/`)
- **Neo4j Support**: Native Neo4j integration via `langchain-neo4j`
- **Memgraph Support**: In-memory graph processing
- **Relationship Extraction**: Automatic entity relationship detection and storage

### Advanced Features
- **Multimodal Support**: Text and vision processing capabilities
- **Performance Monitoring**: Optional performance tracking with `PerformanceMonitor`
- **Telemetry System**: Built-in analytics and usage tracking
- **Context Variables**: OpenMemory-style identity management for enterprise scenarios

## Component Deep Dive

### LLM Providers (34+ supported)
Core providers in `mem0/llms/`:
- **OpenAI**: `openai.py`, `openai_structured.py` (structured output)
- **Anthropic**: `anthropic.py` (Claude models)
- **Azure**: `azure_openai.py`, `azure_openai_structured.py`
- **AWS Bedrock**: `aws_bedrock.py` (multiple model families)
- **Open Source**: `ollama.py`, `vllm.py`, `lmstudio.py`
- **Commercial**: `groq.py`, `together.py`, `deepseek.py`, `xai.py`

### Vector Stores (16+ supported)
Core integrations in `mem0/vector_stores/`:
- **Cloud**: `pinecone.py`, `weaviate.py`, `qdrant.py`, `chroma.py`
- **Enterprise**: `azure_ai_search.py`, `elasticsearch.py`, `opensearch.py`
- **Database**: `pgvector.py`, `mongodb.py`, `redis.py`, `supabase.py`
- **Local**: `faiss.py`, `milvus.py`

### Embedding Providers (9+ supported)
Available in `mem0/embeddings/`:
- **OpenAI**: `openai.py` (text-embedding-ada-002, text-embedding-3-small/large)
- **HuggingFace**: `huggingface.py` (local and hosted models)
- **Cloud**: `azure_openai.py`, `gemini.py`, `vertexai.py`, `together.py`
- **Local**: `ollama.py`, `lmstudio.py`

## Testing Framework

### Test Organization
- **Unit Tests**: Component-specific tests in `tests/` with mocking
- **Integration Tests**: Cross-component testing in `tests/integration/`
- **Pytest Configuration**: Uses pytest with asyncio support, parametrized tests
- **Mocking Strategy**: Extensive use of `unittest.mock` for external dependencies
- **Test Fixtures**: Reusable test components with proper teardown

### Key Test Patterns
```python
# Fixture pattern for Memory instances
@pytest.fixture
def memory_instance():
    with patch("mem0.utils.factory.EmbedderFactory"), \
         patch("mem0.memory.main.VectorStoreFactory"), \
         patch("mem0.utils.factory.LlmFactory"):
        # Mock setup and return configured Memory instance
```

## OpenMemory UI Architecture

### Frontend Stack (`openmemory/ui/`)
- **Framework**: Next.js 15 with App Router
- **Language**: TypeScript with strict type checking
- **Styling**: Tailwind CSS with custom design system
- **Components**: Radix UI primitives with shadcn/ui components
- **State Management**: Redux Toolkit with typed slices
- **Package Manager**: pnpm with optimization settings

### UI Architecture Patterns
1. **State Slices**: Modular Redux slices in `store/`
   - `memoriesSlice.ts`: Memory data management
   - `appsSlice.ts`: Application integration data
   - `uiSlice.ts`: UI state and preferences
   - `filtersSlice.ts`: Search and filter state
   - `configSlice.ts`: Configuration management

2. **Component Organization**:
   - **Pages**: App Router pages in `app/` directory
   - **Components**: Reusable UI components in `components/`
   - **Hooks**: Custom React hooks in `hooks/`
   - **Utils**: Helper functions in `lib/`

3. **API Integration**: Custom hooks for backend communication
   - `useMemoriesApi.ts`: Memory CRUD operations
   - `useAppsApi.ts`: Application management
   - `useFiltersApi.ts`: Search and filtering

### Development Commands
```bash
# OpenMemory UI development
cd openmemory/ui
pnpm install          # Install dependencies
pnpm dev             # Start development server
pnpm build           # Production build
pnpm lint            # Run ESLint
```

### Local API Server
```bash
# Start local Mem0 API server (from server/ directory)
cd server
python main.py       # Starts FastAPI server on http://localhost:8000

# Access API documentation
curl http://localhost:8000/openapi.json    # Get OpenAPI spec
open http://localhost:8000/docs            # Swagger UI
open http://localhost:8000/redoc           # ReDoc interface
```

## MCP Server Implementation

### Architecture (`mem0_mcp/`)
Production-ready Model Context Protocol server with:

1. **Protocol Compliance**: Full MCP specification support
   - Versions: 2025-03-26, 2024-11-05, 2024-10-07
   - JSON-RPC 2.0 transport layer
   - HTTP and Server-Sent Events support

2. **Identity Management**: OpenMemory-style context variables
   - Multiple endpoint formats for enterprise integration
   - Session scoping with user/agent/run identifiers
   - Backward compatibility with explicit parameters

3. **Available MCP Tools**:
   - `add_memory`: Add memories from conversation messages
   - `search_memories`: Natural language memory search
   - `get_memories`: Retrieve memories with filtering
   - `get_memory_by_id`: Get specific memory by ID
   - `delete_memory`: Delete individual memories
   - `batch_delete_memories`: Bulk memory deletion

### MCP Server Structure
```
mem0_mcp/src/
├── server.py                 # Main server coordination
├── protocol/
│   ├── mcp_handler.py        # MCP protocol implementation
│   ├── jsonrpc.py           # JSON-RPC message handling
│   └── message_types.py     # MCP message schemas
├── transport/
│   ├── http_transport.py    # HTTP transport layer
│   └── base_transport.py    # Transport abstraction
├── tools/
│   ├── memory_tools.py      # Memory operation tools
│   └── tool_registry.py    # Tool registration system
└── client/
    └── mem0_client.py       # Mem0 API client adapter
```

## Development Best Practices

### Code Organization
1. **Separation of Concerns**: Clear boundaries between components
2. **Dependency Injection**: Factory pattern for component creation
3. **Configuration Management**: Environment-aware configuration system
4. **Error Handling**: Comprehensive error handling with custom exceptions
5. **Logging**: Structured logging throughout the application

### API Documentation Guidelines
1. **Dynamic OpenAPI Generation**: Always use FastAPI's auto-generated OpenAPI specs
   - **Local Development**: `http://localhost:8000/openapi.json` for JSON spec
   - **Interactive Docs**: `http://localhost:8000/docs` for Swagger UI
   - **Alternative Docs**: `http://localhost:8000/redoc` for ReDoc interface
2. **Avoid Static OpenAPI Files**: Do not commit static `*_openapi.json` files to version control
3. **Documentation Workflow**: Use CI/CD pipelines to auto-generate and deploy API docs
4. **Version Management**: API versions are controlled through code, not static files

### Memory Management Patterns
1. **Session Scoping**: Consistent use of user_id/agent_id/run_id
2. **Metadata Filtering**: Rich metadata support for memory organization
3. **Caching**: Context-aware caching with TTL management
4. **Performance**: Optional performance monitoring and optimization

### Testing Strategies
1. **Mock External Dependencies**: Vector stores, LLMs, embedding providers
2. **Parametrized Tests**: Test multiple configurations and providers
3. **Integration Testing**: Cross-language consistency testing
4. **Performance Testing**: Memory operations under load

## Key Files & Entry Points

### Core Library
- `mem0/__init__.py`: Main exports (`Memory`, `AsyncMemory`, clients)
- `mem0/memory/main.py`: Core Memory class (1000+ lines)
- `mem0/configs/base.py`: Configuration system with environment support
- `mem0/utils/factory.py`: Factory pattern implementation for all providers

### Configuration Files
- `pyproject.toml`: Package metadata, dependencies, hatch configuration
- `Makefile`: Development workflow automation
- `mem0/configs/prompts.py`: LLM prompts for memory processing

### UI Development
- `openmemory/ui/package.json`: Frontend dependencies and scripts
- `openmemory/ui/app/layout.tsx`: Main application layout
- `openmemory/ui/store/store.ts`: Redux store configuration

### MCP Server
- `mem0_mcp/run_server.py`: MCP server entry point
- `mem0_mcp/src/server.py`: Main server implementation
- `mem0_mcp/requirements.txt`: Python dependencies for MCP server

## Environment Variables

### Core Configuration
- `MEM0_DIR`: Base directory for Mem0 data (default: `~/.mem0`)
- `MEM0_DATA_PATH`: Data directory path (default: `./data`)
- `MEM0_HISTORY_DB_PATH`: SQLite database path
- `MEM0_VECTOR_STORAGE_PATH`: Vector storage directory
- `OPENAI_API_KEY`: Required for default LLM and embeddings

### Provider-Specific
Each provider may require specific environment variables for API keys, endpoints, and configuration. Check the respective provider documentation in `mem0/llms/`, `mem0/embeddings/`, or `mem0/vector_stores/` directories.