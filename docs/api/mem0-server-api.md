# Mem0 Server API 文档

## 概述

本文档记录了Mem0 Server的实际API能力和使用方法，基于 http://localhost:8000/docs 的官方API规范。

## 基础信息

- **基础URL**: `http://localhost:8000`
- **API版本**: `v1`
- **认证方式**: 无需认证（开发环境）
- **数据格式**: JSON

## API端点列表

### 1. 记忆管理 API

#### 获取记忆列表

```http
GET /v1/memories/
```

**查询参数:**

| 参数 | 类型 | 必需 | 描述 |
|------|------|------|------|
| user_id | string | 否 | 用户ID过滤 |
| run_id | string | 否 | 运行ID过滤 |
| agent_id | string | 否 | 智能体ID过滤 |
| limit | integer | 否 | 返回数量限制 |
| offset | integer | 否 | 偏移量 |

**响应示例:**

```json
{
  "memories": [
    {
      "id": "mem_123",
      "content": "用户喜欢喝咖啡",
      "user_id": "user_001",
      "run_id": "run_001",
      "agent_id": "agent_001",
      "custom_categories": ["preferences", "food"],
      "created_at": "2024-01-28T10:00:00Z",
      "updated_at": "2024-01-28T10:00:00Z"
    }
  ],
  "total": 1
}
```

#### 创建记忆

```http
POST /v1/memories/
```

**请求体:**

```json
{
  "content": "用户喜欢喝咖啡",
  "user_id": "user_001",
  "run_id": "run_001",
  "agent_id": "agent_001",
  "custom_categories": ["preferences", "food"]
}
```

**响应示例:**

```json
{
  "id": "mem_123",
  "content": "用户喜欢喝咖啡",
  "user_id": "user_001",
  "run_id": "run_001",
  "agent_id": "agent_001",
  "custom_categories": ["preferences", "food"],
  "created_at": "2024-01-28T10:00:00Z",
  "updated_at": "2024-01-28T10:00:00Z"
}
```

#### 获取特定记忆

```http
GET /v1/memories/{memory_id}
```

**路径参数:**

| 参数 | 类型 | 必需 | 描述 |
|------|------|------|------|
| memory_id | string | 是 | 记忆ID |

#### 更新记忆

```http
PUT /v1/memories/{memory_id}
```

**请求体:**

```json
{
  "content": "更新后的记忆内容",
  "custom_categories": ["updated", "category"]
}
```

#### 删除记忆

```http
DELETE /v1/memories/{memory_id}
```

**响应:**

```json
{
  "message": "Memory deleted successfully"
}
```

### 2. 搜索 API

#### 搜索记忆

```http
POST /v1/memories/search/
```

**请求体:**

```json
{
  "query": "咖啡",
  "user_id": "user_001",
  "run_id": "run_001",
  "agent_id": "agent_001",
  "limit": 10
}
```

**响应示例:**

```json
{
  "memories": [
    {
      "id": "mem_123",
      "content": "用户喜欢喝咖啡",
      "user_id": "user_001",
      "run_id": "run_001",
      "agent_id": "agent_001",
      "custom_categories": ["preferences", "food"],
      "created_at": "2024-01-28T10:00:00Z",
      "updated_at": "2024-01-28T10:00:00Z",
      "score": 0.95
    }
  ],
  "total": 1
}
```

### 3. 统计 API

#### 获取系统统计

```http
GET /v1/stats/
```

**响应示例:**

```json
{
  "total_memories": 150,
  "total_users": 5,
  "memories_today": 12,
  "avg_response_time": 250
}
```

### 4. 健康检查 API

#### 系统健康状态

```http
GET /v1/health/
```

**响应示例:**

```json
{
  "status": "ok",
  "timestamp": "2024-01-28T10:00:00Z"
}
```

## 不存在的API端点

以下API端点在实际的Mem0 Server中**不存在**，UI组件已实现替代方案：

### ❌ 用户管理 API

```http
GET /v1/users/          # 不存在
POST /v1/users/         # 不存在
PUT /v1/users/{id}      # 不存在
DELETE /v1/users/{id}   # 不存在
```

**替代方案:** 从记忆数据中推断用户列表

```typescript
const getUsers = async (): Promise<string[]> => {
  const { memories } = await realMem0Client.getMemories();
  const users = [...new Set(memories.map(m => m.user_id).filter(Boolean))];
  return users.length > 0 ? users : ['default'];
};
```

### ❌ 应用管理 API

```http
GET /v1/apps/           # 不存在
POST /v1/apps/          # 不存在
PUT /v1/apps/{id}       # 不存在
DELETE /v1/apps/{id}    # 不存在
```

**替代方案:** 从记忆数据中推断应用列表

```typescript
const getApps = async (): Promise<string[]> => {
  const { memories } = await realMem0Client.getMemories();
  const apps = [...new Set(memories.map(m => m.run_id).filter(Boolean))];
  return apps.length > 0 ? apps : ['default'];
};
```

### ❌ 活动记录 API

```http
GET /v1/activities/     # 不存在
POST /v1/activities/    # 不存在
```

**替代方案:** 基于记忆的时间戳构建活动时间线

```typescript
const getMemoryHistory = async (): Promise<MemoryHistoryItem[]> => {
  const { memories } = await realMem0Client.getMemories();
  return memories.map(memory => ({
    id: `history-${memory.id}`,
    memory_id: memory.id,
    operation: 'create',
    timestamp: memory.created_at,
    user_id: memory.user_id,
    run_id: memory.run_id,
    agent_id: memory.agent_id,
    categories: memory.custom_categories || []
  }));
};
```

### ❌ 分类管理 API

```http
GET /v1/categories/     # 不存在
POST /v1/categories/    # 不存在
```

**替代方案:** 从记忆数据中提取分类

```typescript
const getAvailableCategories = async (): Promise<string[]> => {
  const { memories } = await realMem0Client.getMemories();
  const categories = memories.flatMap(m => m.custom_categories || []);
  return [...new Set(categories)];
};
```

## API客户端实现

### RealMem0APIClient 类

```typescript
class RealMem0APIClient {
  private baseURL: string;
  private axiosInstance: AxiosInstance;

  constructor(baseURL: string = 'http://localhost:8000') {
    this.baseURL = baseURL;
    this.axiosInstance = axios.create({
      baseURL,
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json',
      },
    });
  }

  // 获取记忆列表
  async getMemories(params?: GetMemoriesParams): Promise<GetMemoriesResponse> {
    const response = await this.axiosInstance.get('/v1/memories/', { params });
    return response.data;
  }

  // 创建记忆
  async createMemory(data: CreateMemoryRequest): Promise<Memory> {
    const response = await this.axiosInstance.post('/v1/memories/', data);
    return response.data;
  }

  // 搜索记忆
  async searchMemories(data: SearchMemoriesRequest): Promise<SearchMemoriesResponse> {
    const response = await this.axiosInstance.post('/v1/memories/search/', data);
    return response.data;
  }

  // 获取统计信息
  async getStats(): Promise<StatsResponse> {
    const response = await this.axiosInstance.get('/v1/stats/');
    return response.data;
  }

  // 健康检查
  async healthCheck(): Promise<HealthCheckResponse> {
    const response = await this.axiosInstance.get('/v1/health/');
    return response.data;
  }
}
```

## 错误处理

### 常见错误码

| 状态码 | 描述 | 处理方式 |
|--------|------|----------|
| 200 | 成功 | 正常处理响应数据 |
| 400 | 请求参数错误 | 检查请求参数格式 |
| 404 | 资源不存在 | 显示友好的错误信息 |
| 500 | 服务器内部错误 | 提供重试机制 |

### 错误处理示例

```typescript
try {
  const memories = await realMem0Client.getMemories();
  return memories;
} catch (error) {
  if (axios.isAxiosError(error)) {
    const status = error.response?.status;
    const message = error.response?.data?.message || error.message;
    
    switch (status) {
      case 400:
        throw new Error(`请求参数错误: ${message}`);
      case 404:
        throw new Error(`资源不存在: ${message}`);
      case 500:
        throw new Error(`服务器错误: ${message}`);
      default:
        throw new Error(`网络错误: ${message}`);
    }
  }
  throw error;
}
```

## 最佳实践

### 1. API调用优化

- 使用适当的超时设置
- 实现请求重试机制
- 合理使用缓存
- 避免频繁的API调用

### 2. 错误处理

- 提供用户友好的错误信息
- 实现优雅降级
- 记录错误日志
- 提供重试选项

### 3. 数据验证

- 客户端验证请求参数
- 服务端响应数据验证
- 类型安全检查
- 边界条件处理

### 4. 性能优化

- 使用分页加载大量数据
- 实现数据缓存机制
- 合并多个API调用
- 使用防抖和节流

## 开发环境配置

### 启动Mem0 Server

```bash
# 确保Mem0 Server在8000端口运行
curl http://localhost:8000/v1/health/
```

### 环境变量

```env
# .env.local
NEXT_PUBLIC_MEM0_API_URL=http://localhost:8000
NEXT_PUBLIC_API_TIMEOUT=10000
```

### 开发工具

- **API文档**: http://localhost:8000/docs
- **健康检查**: http://localhost:8000/v1/health/
- **浏览器开发者工具**: 网络面板监控API调用

## 更新日志

### v1.0.0 (2024-01-28)

- ✅ 完成实际API能力分析
- ✅ 修复API端点格式问题
- ✅ 实现不存在API的替代方案
- ✅ 添加完整的错误处理机制
- ✅ 提供详细的使用文档和示例
