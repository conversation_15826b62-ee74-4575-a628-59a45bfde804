version: '3.8'

services:
  mem0-mcp-server:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: mem0-mcp-server
    restart: unless-stopped
    ports:
      - "8001:8001"
    environment:
      - MCP_HOST=0.0.0.0
      - MCP_PORT=8001
      - MCP_DEBUG=false
      - MEM0_BASE_URL=http://mem0-server:8000
      - MEM0_API_VERSION=v1
      - MCP_LOG_LEVEL=INFO
      - MCP_LOG_TO_FILE=true
      - MCP_LOG_FILE_PATH=/app/logs/server.log
      - MCP_MAX_CONCURRENT_REQUESTS=100
      - MCP_REQUEST_TIMEOUT=30
    volumes:
      - ./logs:/app/logs
      - ./data:/app/data
    networks:
      - mem0-network
    depends_on:
      - mem0-server
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8001/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Example Mem0 server service (adjust according to your setup)
  mem0-server:
    image: mem0ai/mem0:latest  # Replace with actual Mem0 image
    container_name: mem0-server
    restart: unless-stopped
    ports:
      - "8000:8000"
    environment:
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - QDRANT_URL=http://qdrant:6333
      - NEO4J_URL=bolt://neo4j:7687
      - NEO4J_USERNAME=neo4j
      - NEO4J_PASSWORD=password
    networks:
      - mem0-network
    depends_on:
      - qdrant
      - neo4j
    volumes:
      - mem0-data:/app/data

  # Vector database
  qdrant:
    image: qdrant/qdrant:latest
    container_name: qdrant
    restart: unless-stopped
    ports:
      - "6333:6333"
    volumes:
      - qdrant-data:/qdrant/storage
    networks:
      - mem0-network

  # Graph database
  neo4j:
    image: neo4j:5
    container_name: neo4j
    restart: unless-stopped
    ports:
      - "7474:7474"
      - "7687:7687"
    environment:
      - NEO4J_AUTH=neo4j/password
      - NEO4J_PLUGINS=["apoc"]
    volumes:
      - neo4j-data:/data
      - neo4j-logs:/logs
    networks:
      - mem0-network

  # Optional: Nginx reverse proxy
  nginx:
    image: nginx:alpine
    container_name: mem0-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    networks:
      - mem0-network
    depends_on:
      - mem0-mcp-server
      - mem0-server

networks:
  mem0-network:
    driver: bridge

volumes:
  mem0-data:
  qdrant-data:
  neo4j-data:
  neo4j-logs: