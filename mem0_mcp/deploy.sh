#!/bin/bash

# Mem0 MCP Server Deployment Script
# This script handles installation, configuration, and deployment of the Mem0 MCP service

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
PROJECT_DIR="/opt/mem0ai/mem0_mcp"
SERVICE_NAME="mem0-mcp-server"
SERVICE_USER="mem0"
VENV_PATH="$PROJECT_DIR/venv"
SYSTEMD_SERVICE_FILE="/etc/systemd/system/$SERVICE_NAME.service"

# Functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

check_requirements() {
    log_info "Checking system requirements..."
    
    # Check Python version
    if ! command -v python3 &> /dev/null; then
        log_error "Python 3 is required but not installed"
        exit 1
    fi
    
    python_version=$(python3 --version | cut -d' ' -f2 | cut -d'.' -f1,2)
    required_version="3.8"
    
    if ! python3 -c "import sys; exit(0 if sys.version_info >= (3, 8) else 1)"; then
        log_error "Python 3.8+ is required, found $python_version"
        exit 1
    fi
    
    # Check pip
    if ! command -v pip3 &> /dev/null; then
        log_error "pip3 is required but not installed"
        exit 1
    fi
    
    log_success "System requirements check passed"
}

create_user_and_directories() {
    log_info "Creating service user and directories..."
    
    # Create service user if doesn't exist
    if ! id "$SERVICE_USER" &>/dev/null; then
        sudo useradd --system --shell /bin/false --home-dir /var/lib/$SERVICE_USER --create-home $SERVICE_USER
        log_success "Created service user: $SERVICE_USER"
    else
        log_info "Service user $SERVICE_USER already exists"
    fi
    
    # Ensure project directory exists and has correct permissions
    sudo mkdir -p "$PROJECT_DIR"
    sudo chown $SERVICE_USER:$SERVICE_USER "$PROJECT_DIR"
    
    # Create log directory
    sudo mkdir -p /var/log/$SERVICE_NAME
    sudo chown $SERVICE_USER:$SERVICE_USER /var/log/$SERVICE_NAME
    
    log_success "User and directories created"
}

install_dependencies() {
    log_info "Installing Python dependencies..."
    
    # Create virtual environment
    sudo -u $SERVICE_USER python3 -m venv "$VENV_PATH"
    
    # Upgrade pip
    sudo -u $SERVICE_USER "$VENV_PATH/bin/pip" install --upgrade pip
    
    # Install requirements
    sudo -u $SERVICE_USER "$VENV_PATH/bin/pip" install -r "$PROJECT_DIR/requirements.txt"
    
    log_success "Dependencies installed"
}

create_systemd_service() {
    log_info "Creating systemd service..."
    
    sudo tee "$SYSTEMD_SERVICE_FILE" > /dev/null <<EOF
[Unit]
Description=Mem0 MCP Server
After=network.target
Wants=network.target

[Service]
Type=exec
User=$SERVICE_USER
Group=$SERVICE_USER
WorkingDirectory=$PROJECT_DIR
Environment=PATH=$VENV_PATH/bin
Environment=PYTHONPATH=$PROJECT_DIR/src
ExecStart=$VENV_PATH/bin/python run_server.py
ExecReload=/bin/kill -HUP \$MAINPID
Restart=always
RestartSec=10
SyslogIdentifier=$SERVICE_NAME

# Security settings
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ReadWritePaths=/var/log/$SERVICE_NAME
ProtectHome=true
ProtectKernelTunables=true
ProtectKernelModules=true
ProtectControlGroups=true

# Resource limits
LimitNOFILE=65536
LimitNPROC=4096

[Install]
WantedBy=multi-user.target
EOF
    
    # Reload systemd and enable service
    sudo systemctl daemon-reload
    sudo systemctl enable "$SERVICE_NAME"
    
    log_success "Systemd service created and enabled"
}

create_config_file() {
    log_info "Creating configuration file..."
    
    CONFIG_FILE="$PROJECT_DIR/.env"
    
    sudo -u $SERVICE_USER tee "$CONFIG_FILE" > /dev/null <<EOF
# Mem0 MCP Server Configuration
# Generated by deployment script on $(date)

# Server settings
MCP_HOST=localhost
MCP_PORT=8001
MCP_DEBUG=false

# Mem0 service settings
MEM0_BASE_URL=http://localhost:8000
MEM0_API_VERSION=v1

# Transport settings
MCP_TRANSPORT=http
MCP_ENABLE_STREAMING=true

# Logging settings
MCP_LOG_LEVEL=INFO
MCP_LOG_TO_FILE=true
MCP_LOG_FILE_PATH=/var/log/$SERVICE_NAME/server.log

# Performance settings
MCP_MAX_CONCURRENT_REQUESTS=100
MCP_REQUEST_TIMEOUT=30
EOF
    
    log_success "Configuration file created at $CONFIG_FILE"
    log_warning "Please review and modify the configuration as needed"
}

run_tests() {
    log_info "Running tests..."
    
    cd "$PROJECT_DIR"
    
    # Install test dependencies
    sudo -u $SERVICE_USER "$VENV_PATH/bin/pip" install pytest pytest-asyncio
    
    # Run tests
    if sudo -u $SERVICE_USER "$VENV_PATH/bin/python" -m pytest tests/ -v; then
        log_success "All tests passed"
    else
        log_warning "Some tests failed, but continuing deployment"
    fi
}

start_service() {
    log_info "Starting service..."
    
    sudo systemctl start "$SERVICE_NAME"
    
    # Wait a moment and check status
    sleep 2
    
    if sudo systemctl is-active --quiet "$SERVICE_NAME"; then
        log_success "Service started successfully"
        sudo systemctl status "$SERVICE_NAME" --no-pager -l
    else
        log_error "Service failed to start"
        sudo journalctl -u "$SERVICE_NAME" --no-pager -l
        exit 1
    fi
}

show_status() {
    log_info "Service status:"
    sudo systemctl status "$SERVICE_NAME" --no-pager -l
    
    log_info "Service logs (last 20 lines):"
    sudo journalctl -u "$SERVICE_NAME" -n 20 --no-pager
    
    log_info "Health check:"
    if curl -s http://localhost:8001/health > /dev/null; then
        log_success "Service is responding to health checks"
    else
        log_warning "Service is not responding to health checks"
    fi
}

print_usage() {
    echo "Usage: $0 {install|start|stop|restart|status|logs|uninstall}"
    echo ""
    echo "Commands:"
    echo "  install   - Full installation and setup"
    echo "  start     - Start the service"
    echo "  stop      - Stop the service"
    echo "  restart   - Restart the service"
    echo "  status    - Show service status"
    echo "  logs      - Show service logs"
    echo "  uninstall - Remove service and cleanup"
    echo ""
}

# Main script logic
case "${1:-install}" in
    install)
        log_info "Starting Mem0 MCP Server installation..."
        check_requirements
        create_user_and_directories
        install_dependencies
        create_systemd_service
        create_config_file
        run_tests
        start_service
        show_status
        log_success "Installation completed successfully!"
        echo ""
        log_info "Next steps:"
        echo "  1. Review configuration: $PROJECT_DIR/.env"
        echo "  2. Check service logs: sudo journalctl -u $SERVICE_NAME -f"
        echo "  3. Test the service: curl http://localhost:8001/health"
        ;;
    
    start)
        sudo systemctl start "$SERVICE_NAME"
        log_success "Service started"
        ;;
    
    stop)
        sudo systemctl stop "$SERVICE_NAME"
        log_success "Service stopped"
        ;;
    
    restart)
        sudo systemctl restart "$SERVICE_NAME"
        log_success "Service restarted"
        ;;
    
    status)
        show_status
        ;;
    
    logs)
        sudo journalctl -u "$SERVICE_NAME" -f
        ;;
    
    uninstall)
        log_info "Uninstalling Mem0 MCP Server..."
        sudo systemctl stop "$SERVICE_NAME" 2>/dev/null || true
        sudo systemctl disable "$SERVICE_NAME" 2>/dev/null || true
        sudo rm -f "$SYSTEMD_SERVICE_FILE"
        sudo systemctl daemon-reload
        
        # Optionally remove user and files
        read -p "Remove service user and files? [y/N]: " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            sudo userdel "$SERVICE_USER" 2>/dev/null || true
            sudo rm -rf "/var/lib/$SERVICE_USER"
            sudo rm -rf "/var/log/$SERVICE_NAME"
            log_success "User and files removed"
        fi
        
        log_success "Uninstallation completed"
        ;;
    
    *)
        print_usage
        exit 1
        ;;
esac