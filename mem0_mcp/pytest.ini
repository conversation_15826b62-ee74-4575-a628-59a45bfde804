[tool:pytest]
python_files = test_*.py
python_classes = Test*
python_functions = test_*
testpaths = tests
addopts = 
    -v
    --tb=short
    --strict-markers
    --disable-warnings
    --asyncio-mode=auto

markers =
    unit: Unit tests
    integration: Integration tests
    performance: Performance tests
    slow: Slow running tests

asyncio_mode = auto
timeout = 60

filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning