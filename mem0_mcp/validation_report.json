{"status": "SUCCESS", "passed": 6, "warned": 0, "failed": 0, "total": 6, "details": [{"test": "Server Health Check", "status": "PASS", "details": "Status: healthy", "timestamp": **********.5927584}, {"test": "MCP Initialization", "status": "PASS", "details": "Server: mem0-mcp-server", "timestamp": **********.6473188}, {"test": "Tools List", "status": "PASS", "details": "Found 6 tools", "timestamp": **********.699261}, {"test": "Memory Operations", "status": "PASS", "details": "add_memory tool executed successfully", "timestamp": **********.8236306}, {"test": "Erro<PERSON>", "status": "PASS", "details": "JSON parse error handled correctly", "timestamp": **********.8742948}, {"test": "Concurrent Requests", "status": "PASS", "details": "10/10 requests successful", "timestamp": **********.9377031}]}