# Graph Memory技术实施细节文档

## 1. 技术栈选型与依赖管理

### 1.1 新增依赖包
```json
{
  "dependencies": {
    // 图可视化核心库
    "d3": "^7.8.5",
    "@types/d3": "^7.4.0",
    "react-d3-graph": "^2.6.0",
    
    // 或者选择React Flow（备选方案）
    "reactflow": "^11.10.1",
    "@reactflow/core": "^11.10.1",
    "@reactflow/controls": "^11.2.7",
    "@reactflow/minimap": "^11.7.8",
    
    // 图算法库
    "graphology": "^0.25.1",
    "graphology-layout": "^0.6.1",
    "graphology-layout-force": "^0.2.1",
    
    // 性能优化
    "react-virtualized": "^9.22.5",
    "react-window": "^1.8.8"
  }
}
```

### 1.2 技术选型决策矩阵
| 方案 | 学习成本 | 性能 | 功能完整性 | 社区支持 | 开发效率 | 推荐度 |
|------|----------|------|------------|----------|----------|--------|
| D3.js | 高 | 优秀 | 完整 | 优秀 | 低 | ⭐⭐⭐⭐⭐ |
| React Flow | 低 | 良好 | 良好 | 良好 | 高 | ⭐⭐⭐⭐ |
| Vis.js | 中 | 良好 | 良好 | 中等 | 中 | ⭐⭐⭐ |

**最终选型决策**：React Flow（调整推荐方案）
- **选择理由**：考虑到2-3周开发周期的时间约束，React Flow的低学习成本和高开发效率更适合项目需求
- **优势**：
  - 开箱即用的React组件
  - 内置布局算法和交互功能
  - 良好的TypeScript支持
  - 丰富的示例和文档
- **劣势**：定制化程度相对较低
- **适用场景**：快速开发图可视化应用，满足MVP需求

**技术预研验证计划**：
```typescript
// Phase 0: 环境验证（2天）
const technicalValidation = {
  day1: [
    '验证Mem0 Server的Graph Memory API功能',
    '测试enable_graph=true参数的API响应',
    '确认relations数据结构和格式',
    '验证Neo4j连接和数据查询'
  ],
  day2: [
    '安装React Flow并创建基础示例',
    '实现Mem0 Graph Memory数据到React Flow的转换',
    '测试基础节点和边的渲染功能',
    '验证与现有Next.js项目的兼容性'
  ]
};
```

## 2. 数据模型设计

### 2.1 核心数据结构
```typescript
// 实体数据模型
interface Entity {
  id: string;
  name: string;
  type: EntityType;
  properties: Record<string, any>;
  position?: { x: number; y: number };
  graphId: string;
  createdAt: string;
  updatedAt: string;
  metadata: {
    memoryId: string;  // 对应的Memory记录ID
    color?: string;
    size?: number;
    icon?: string;
  };
}

// 关系数据模型
interface Relationship {
  id: string;
  sourceId: string;
  targetId: string;
  type: RelationshipType;
  properties: Record<string, any>;
  weight?: number;
  graphId: string;
  createdAt: string;
  updatedAt: string;
  metadata: {
    memoryId: string;  // 对应的Memory记录ID
    color?: string;
    style?: 'solid' | 'dashed' | 'dotted';
    label?: string;
  };
}

// 图记忆数据模型
interface GraphMemory {
  id: string;
  name: string;
  description?: string;
  entities: Entity[];
  relationships: Relationship[];
  layout: GraphLayout;
  settings: GraphSettings;
  stats: GraphStats;
  createdAt: string;
  updatedAt: string;
}

// 图统计信息
interface GraphStats {
  entityCount: number;
  relationshipCount: number;
  density: number;
  avgDegree: number;
  connectedComponents: number;
  lastUpdated: string;
}
```

### 2.2 Mem0 Graph Memory API集成

#### 核心API端点和参数
```typescript
// Mem0 Server API端点（统一使用8000端口）
const MEM0_API_BASE = 'http://localhost:8000';

const API_ENDPOINTS = {
  // 创建带图关系的记忆
  CREATE_MEMORY: `${MEM0_API_BASE}/v1/memories/`,

  // 获取带图关系的记忆
  GET_MEMORIES: `${MEM0_API_BASE}/v1/memories/`,

  // 搜索带图关系的记忆
  SEARCH_MEMORIES: `${MEM0_API_BASE}/v1/memories/search/`,

  // 删除记忆
  DELETE_MEMORY: `${MEM0_API_BASE}/v1/memories/{memory_id}`,

  // 更新记忆
  UPDATE_MEMORY: `${MEM0_API_BASE}/v1/memories/{memory_id}`
};

// Graph Memory关键参数
interface GraphMemoryParams {
  enable_graph: boolean;      // 启用图记忆处理
  output_format: string;      // "v1.1" 返回关系数据
  user_id: string;           // 用户标识
  agent_id?: string;         // 代理标识（可选）
  run_id?: string;           // 运行标识（可选）
}

// Mem0 Graph Memory响应格式
interface GraphMemoryResponse {
  results: Memory[];          // 标准记忆数据
  relations: Relationship[];  // 图关系数据
}
```

## 3. 状态管理架构

### 3.1 Redux Store结构
```typescript
interface GraphMemoryState {
  // 数据状态
  graphs: Record<string, GraphMemory>;
  currentGraphId: string | null;
  entities: Record<string, Entity>;
  relationships: Record<string, Relationship>;
  
  // UI状态
  selectedEntityIds: string[];
  selectedRelationshipIds: string[];
  viewMode: 'network' | 'hierarchy' | 'cluster';
  zoomLevel: number;
  centerPosition: { x: number; y: number };
  
  // 操作状态
  isLoading: boolean;
  error: string | null;
  isDirty: boolean;
  lastSaved: string | null;
  
  // 历史记录
  history: GraphOperation[];
  historyIndex: number;
  
  // 性能优化
  visibleEntityIds: string[];  // 当前视口内的实体
  renderLevel: number;         // 渲染层级（LOD）
}
```

### 3.2 Actions设计
```typescript
// 同步Actions
export const graphMemoryActions = {
  // 图管理
  setCurrentGraph: (graphId: string) => ({ type: 'SET_CURRENT_GRAPH', payload: graphId }),
  addGraph: (graph: GraphMemory) => ({ type: 'ADD_GRAPH', payload: graph }),
  updateGraph: (graphId: string, updates: Partial<GraphMemory>) => ({ 
    type: 'UPDATE_GRAPH', 
    payload: { graphId, updates } 
  }),
  
  // 实体管理
  addEntity: (entity: Entity) => ({ type: 'ADD_ENTITY', payload: entity }),
  updateEntity: (entityId: string, updates: Partial<Entity>) => ({ 
    type: 'UPDATE_ENTITY', 
    payload: { entityId, updates } 
  }),
  removeEntity: (entityId: string) => ({ type: 'REMOVE_ENTITY', payload: entityId }),
  
  // 关系管理
  addRelationship: (relationship: Relationship) => ({ 
    type: 'ADD_RELATIONSHIP', 
    payload: relationship 
  }),
  updateRelationship: (relationshipId: string, updates: Partial<Relationship>) => ({ 
    type: 'UPDATE_RELATIONSHIP', 
    payload: { relationshipId, updates } 
  }),
  removeRelationship: (relationshipId: string) => ({ 
    type: 'REMOVE_RELATIONSHIP', 
    payload: relationshipId 
  }),
  
  // 选择管理
  selectEntity: (entityId: string) => ({ type: 'SELECT_ENTITY', payload: entityId }),
  selectMultipleEntities: (entityIds: string[]) => ({ 
    type: 'SELECT_MULTIPLE_ENTITIES', 
    payload: entityIds 
  }),
  clearSelection: () => ({ type: 'CLEAR_SELECTION' }),
  
  // 视图控制
  setViewMode: (mode: ViewMode) => ({ type: 'SET_VIEW_MODE', payload: mode }),
  setZoomLevel: (level: number) => ({ type: 'SET_ZOOM_LEVEL', payload: level }),
  setCenterPosition: (position: { x: number; y: number }) => ({ 
    type: 'SET_CENTER_POSITION', 
    payload: position 
  }),
  
  // 历史管理
  addToHistory: (operation: GraphOperation) => ({ 
    type: 'ADD_TO_HISTORY', 
    payload: operation 
  }),
  undo: () => ({ type: 'UNDO' }),
  redo: () => ({ type: 'REDO' }),
};

// 异步Actions (Thunks)
export const graphMemoryAsyncActions = {
  // 加载图数据
  loadGraph: (graphId: string) => async (dispatch: AppDispatch, getState: () => RootState) => {
    dispatch(setLoading(true));
    try {
      const graphData = await graphMemoryAPI.getGraphData(graphId);
      dispatch(addGraph(graphData));
      dispatch(setCurrentGraph(graphId));
    } catch (error) {
      dispatch(setError(error.message));
    } finally {
      dispatch(setLoading(false));
    }
  },
  
  // 保存图数据
  saveGraph: (graphId: string) => async (dispatch: AppDispatch, getState: () => RootState) => {
    const state = getState();
    const graph = state.graphMemory.graphs[graphId];
    
    if (!graph || !state.graphMemory.isDirty) return;
    
    dispatch(setLoading(true));
    try {
      await graphMemoryAPI.saveGraphData(graph);
      dispatch(markAsSaved());
    } catch (error) {
      dispatch(setError(error.message));
    } finally {
      dispatch(setLoading(false));
    }
  },
  
  // 创建实体
  createEntity: (entityData: Omit<Entity, 'id' | 'createdAt' | 'updatedAt'>) => 
    async (dispatch: AppDispatch) => {
      dispatch(setLoading(true));
      try {
        const entity = await graphMemoryAPI.createEntity(entityData);
        dispatch(addEntity(entity));
        dispatch(addToHistory({
          type: 'CREATE_ENTITY',
          entityId: entity.id,
          timestamp: Date.now()
        }));
      } catch (error) {
        dispatch(setError(error.message));
      } finally {
        dispatch(setLoading(false));
      }
    },
};
```

## 4. API客户端实现

### 4.1 Graph Memory API客户端
```typescript
class GraphMemoryAPIClient {
  private baseURL: string;
  private headers: Record<string, string>;

  constructor(baseURL: string = 'http://localhost:8000') {
    this.baseURL = baseURL;
    this.headers = {
      'Content-Type': 'application/json',
    };
  }

  // 创建带图关系的记忆
  async createMemoryWithGraph(
    text: string,
    userId: string,
    options?: { agentId?: string; runId?: string }
  ): Promise<GraphMemoryResponse> {
    const response = await fetch(`${this.baseURL}/v1/memories/`, {
      method: 'POST',
      headers: this.headers,
      body: JSON.stringify({
        messages: [{ role: 'user', content: text }],
        user_id: userId,
        agent_id: options?.agentId,
        run_id: options?.runId,
        enable_graph: true,
        output_format: 'v1.1'
      })
    });

    if (!response.ok) {
      throw new Error(`API Error: ${response.status} ${response.statusText}`);
    }

    return response.json();
  }

  // 获取用户的所有图记忆
  async getGraphMemories(
    userId: string,
    options?: { agentId?: string; runId?: string; limit?: number }
  ): Promise<GraphMemoryResponse> {
    const params = new URLSearchParams({
      user_id: userId,
      enable_graph: 'true',
      output_format: 'v1.1',
      limit: (options?.limit || 100).toString()
    });

    if (options?.agentId) params.append('agent_id', options.agentId);
    if (options?.runId) params.append('run_id', options.runId);

    const response = await fetch(`${this.baseURL}/v1/memories/?${params}`, {
      method: 'GET',
      headers: this.headers
    });

    if (!response.ok) {
      throw new Error(`API Error: ${response.status} ${response.statusText}`);
    }

    return response.json();
  }

  // 搜索图记忆
  async searchGraphMemories(
    query: string,
    userId: string,
    options?: { agentId?: string; runId?: string; limit?: number }
  ): Promise<GraphMemoryResponse> {
    const response = await fetch(`${this.baseURL}/v1/memories/search/`, {
      method: 'POST',
      headers: this.headers,
      body: JSON.stringify({
        query,
        user_id: userId,
        agent_id: options?.agentId,
        run_id: options?.runId,
        enable_graph: true,
        output_format: 'v1.1',
        limit: options?.limit || 50
      })
    });

    if (!response.ok) {
      throw new Error(`API Error: ${response.status} ${response.statusText}`);
    }

    return response.json();
  }

  // 删除记忆
  async deleteMemory(memoryId: string): Promise<void> {
    const response = await fetch(`${this.baseURL}/v1/memories/${memoryId}`, {
      method: 'DELETE',
      headers: this.headers
    });

    if (!response.ok) {
      throw new Error(`API Error: ${response.status} ${response.statusText}`);
    }
  }
}

// 单例实例
export const graphMemoryAPI = new GraphMemoryAPIClient();
```

### 4.2 数据转换工具
```typescript
// 将Mem0的relations数据转换为React Flow格式
export class GraphDataTransformer {
  static transformToReactFlow(response: GraphMemoryResponse): {
    nodes: Node[];
    edges: Edge[];
  } {
    const nodes: Node[] = [];
    const edges: Edge[] = [];
    const entityMap = new Map<string, string>();

    // 处理relations数据
    response.relations?.forEach((relation, index) => {
      const sourceId = `entity-${relation.source}`;
      const targetId = `entity-${relation.destination}`;

      // 添加源实体节点
      if (!entityMap.has(relation.source)) {
        nodes.push({
          id: sourceId,
          type: 'entityNode',
          position: { x: Math.random() * 800, y: Math.random() * 600 },
          data: {
            label: relation.source,
            type: 'entity',
            properties: {}
          }
        });
        entityMap.set(relation.source, sourceId);
      }

      // 添加目标实体节点
      if (!entityMap.has(relation.destination)) {
        nodes.push({
          id: targetId,
          type: 'entityNode',
          position: { x: Math.random() * 800, y: Math.random() * 600 },
          data: {
            label: relation.destination,
            type: 'entity',
            properties: {}
          }
        });
        entityMap.set(relation.destination, targetId);
      }

      // 添加关系边
      edges.push({
        id: `edge-${index}`,
        source: sourceId,
        target: targetId,
        type: 'relationshipEdge',
        data: {
          label: relation.relationship,
          type: 'relationship'
        }
      });
    });

    return { nodes, edges };
  }

  static transformFromReactFlow(nodes: Node[], edges: Edge[]): {
    entities: Entity[];
    relationships: Relationship[];
  } {
    const entities: Entity[] = nodes.map(node => ({
      id: node.id,
      name: node.data.label,
      type: node.data.type || 'unknown',
      properties: node.data.properties || {},
      position: node.position,
      graphId: 'default',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      metadata: {
        memoryId: node.data.memoryId || '',
        color: node.data.color,
        size: node.data.size,
        icon: node.data.icon
      }
    }));

    const relationships: Relationship[] = edges.map(edge => ({
      id: edge.id,
      sourceId: edge.source,
      targetId: edge.target,
      type: edge.data?.type || 'unknown',
      properties: edge.data?.properties || {},
      weight: edge.data?.weight || 1,
      graphId: 'default',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      metadata: {
        memoryId: edge.data?.memoryId || '',
        color: edge.data?.color,
        style: edge.data?.style || 'solid',
        label: edge.data?.label
      }
    }));

    return { entities, relationships };
  }
}
```

## 5. 性能优化策略

### 4.1 渲染优化
```typescript
// 层次细节渲染（LOD）
class GraphRenderer {
  private renderLevel: number = 0;
  
  calculateRenderLevel(zoomLevel: number, entityCount: number): number {
    if (zoomLevel > 2 || entityCount < 50) return 3;      // 高细节
    if (zoomLevel > 1 || entityCount < 200) return 2;     // 中细节
    if (zoomLevel > 0.5 || entityCount < 500) return 1;   // 低细节
    return 0; // 最低细节
  }
  
  renderEntity(entity: Entity, level: number): React.ReactNode {
    switch (level) {
      case 3:
        return <DetailedEntityNode entity={entity} />;
      case 2:
        return <StandardEntityNode entity={entity} />;
      case 1:
        return <SimpleEntityNode entity={entity} />;
      case 0:
        return <MinimalEntityNode entity={entity} />;
      default:
        return null;
    }
  }
}

// 视口裁剪
class ViewportCuller {
  calculateVisibleEntities(
    entities: Entity[], 
    viewport: { x: number; y: number; width: number; height: number }
  ): string[] {
    return entities
      .filter(entity => this.isInViewport(entity.position, viewport))
      .map(entity => entity.id);
  }
  
  private isInViewport(
    position: { x: number; y: number }, 
    viewport: { x: number; y: number; width: number; height: number }
  ): boolean {
    return position.x >= viewport.x - 100 &&
           position.x <= viewport.x + viewport.width + 100 &&
           position.y >= viewport.y - 100 &&
           position.y <= viewport.y + viewport.height + 100;
  }
}
```

### 4.2 数据加载优化
```typescript
// 分页加载策略
class GraphDataLoader {
  async loadGraphInChunks(graphId: string, chunkSize: number = 100): Promise<GraphMemory> {
    const metadata = await this.loadGraphMetadata(graphId);
    const entityChunks = await this.loadEntitiesInChunks(graphId, chunkSize);
    const relationshipChunks = await this.loadRelationshipsInChunks(graphId, chunkSize);
    
    return {
      ...metadata,
      entities: entityChunks.flat(),
      relationships: relationshipChunks.flat()
    };
  }
  
  private async loadEntitiesInChunks(graphId: string, chunkSize: number): Promise<Entity[][]> {
    const totalCount = await this.getEntityCount(graphId);
    const chunks: Entity[][] = [];
    
    for (let offset = 0; offset < totalCount; offset += chunkSize) {
      const chunk = await this.loadEntities(graphId, offset, chunkSize);
      chunks.push(chunk);
    }
    
    return chunks;
  }
}

// 缓存策略
class GraphMemoryCache {
  private cache = new Map<string, { data: any; timestamp: number; ttl: number }>();
  
  set(key: string, data: any, ttl: number = 300000): void { // 5分钟TTL
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl
    });
  }
  
  get(key: string): any | null {
    const item = this.cache.get(key);
    if (!item) return null;
    
    if (Date.now() - item.timestamp > item.ttl) {
      this.cache.delete(key);
      return null;
    }
    
    return item.data;
  }
  
  invalidate(pattern: string): void {
    for (const key of this.cache.keys()) {
      if (key.includes(pattern)) {
        this.cache.delete(key);
      }
    }
  }
}
```

## 5. 错误处理与降级策略

### 5.1 错误边界组件
```typescript
class GraphMemoryErrorBoundary extends React.Component<
  { children: React.ReactNode; fallback?: React.ReactNode },
  { hasError: boolean; error?: Error }
> {
  constructor(props: any) {
    super(props);
    this.state = { hasError: false };
  }
  
  static getDerivedStateFromError(error: Error) {
    return { hasError: true, error };
  }
  
  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('Graph Memory Error:', error, errorInfo);
    
    // 发送错误报告
    this.reportError(error, errorInfo);
  }
  
  private reportError(error: Error, errorInfo: React.ErrorInfo) {
    // 错误上报逻辑
    const errorReport = {
      message: error.message,
      stack: error.stack,
      componentStack: errorInfo.componentStack,
      timestamp: Date.now(),
      userAgent: navigator.userAgent,
      url: window.location.href
    };
    
    // 发送到错误监控服务
    fetch('/api/error-report', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(errorReport)
    }).catch(console.error);
  }
  
  render() {
    if (this.state.hasError) {
      return this.props.fallback || (
        <div className="error-fallback bg-gray-900 border border-red-500 rounded-lg p-6">
          <h3 className="text-red-400 text-lg font-semibold mb-2">
            Graph Memory Error
          </h3>
          <p className="text-gray-300 mb-4">
            Something went wrong with the graph visualization. 
            Please try refreshing the page.
          </p>
          <button 
            onClick={() => window.location.reload()}
            className="btn-primary"
          >
            Refresh Page
          </button>
        </div>
      );
    }
    
    return this.props.children;
  }
}
```

### 5.2 功能降级策略
```typescript
// 功能降级管理器
class FeatureDegradationManager {
  private features = new Map<string, boolean>();
  
  constructor() {
    this.initializeFeatures();
  }
  
  private initializeFeatures() {
    // 检测浏览器能力
    this.features.set('webgl', this.checkWebGLSupport());
    this.features.set('canvas', this.checkCanvasSupport());
    this.features.set('svg', this.checkSVGSupport());
    this.features.set('performance', this.checkPerformanceCapability());
  }
  
  getRecommendedRenderer(): 'webgl' | 'canvas' | 'svg' | 'table' {
    if (this.features.get('webgl') && this.features.get('performance')) {
      return 'webgl';
    }
    if (this.features.get('canvas')) {
      return 'canvas';
    }
    if (this.features.get('svg')) {
      return 'svg';
    }
    return 'table'; // 最后的降级方案
  }
  
  private checkWebGLSupport(): boolean {
    try {
      const canvas = document.createElement('canvas');
      return !!(canvas.getContext('webgl') || canvas.getContext('experimental-webgl'));
    } catch {
      return false;
    }
  }
  
  private checkPerformanceCapability(): boolean {
    // 简单的性能检测
    const start = performance.now();
    for (let i = 0; i < 100000; i++) {
      Math.random();
    }
    const duration = performance.now() - start;
    
    return duration < 10; // 10ms内完成表示性能良好
  }
}
```

这个技术实施细节文档补充了Graph Memory功能开发所需的具体技术方案，包括依赖管理、数据模型、状态管理、性能优化和错误处理等关键技术细节。
