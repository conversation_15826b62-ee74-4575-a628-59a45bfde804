# =============================================================================
# Mem0 环境配置文件
# =============================================================================
# 此文件包含 Mem0 部署所需的所有环境变量
# 请复制此文件为 .env 并根据您的设置配置相应的值

# =============================================================================
# OpenAI 配置
# =============================================================================
# OpenAI API 密钥，用于 LLM 和嵌入服务
OPENAI_API_KEY=sk-kP3jyHHUmlQ0HI5D923e0fA8CfBf48C5A60aC5D71061Ac46

# OpenAI 模型配置（默认：gpt-4o-mini）
OPENAI_MODEL=gpt-4o-mini

# OpenAI API 基础 URL（默认：https://api.openai.com/v1）
# 第三方 API 示例：
# DeepSeek: https://api.deepseek.com/v1
# 智谱 AI: https://open.bigmodel.cn/api/paas/v4
OPENAI_BASE_URL=https://aihubmix.com/v1

# OpenAI 嵌入模型（默认：text-embedding-3-small）
OPENAI_EMBEDDING_MODEL=text-embedding-3-small

# OpenAI 模型参数
OPENAI_TEMPERATURE=0.1
OPENAI_MAX_TOKENS=2000

# =============================================================================
# Neo4j 图数据库配置
# =============================================================================
# Neo4j 连接 URL（Docker 默认：bolt://neo4j:7687）
NEO4J_URL=bolt://neo4j:7687
# 旧版支持（已弃用，请使用 NEO4J_URL）
NEO4J_URI=

# Neo4j 身份验证
NEO4J_USERNAME=neo4j
NEO4J_PASSWORD=mem0graph

# Neo4j 数据库名称（默认：neo4j）
NEO4J_DATABASE=neo4j

# 启用/禁用图存储功能（默认：true）
ENABLE_GRAPH_STORE=true

# =============================================================================
# 数据存储配置
# =============================================================================
# 基础数据目录路径（生产环境默认：/var/lib/mem0/data，开发环境：./data）
MEM0_DATA_PATH=./data

# 历史数据库路径（默认：基于 MEM0_DATA_PATH 自动生成）
# MEM0_HISTORY_DB_PATH=

# 向量存储路径（默认：基于 MEM0_DATA_PATH 自动生成）
# MEM0_VECTOR_STORAGE_PATH=

# =============================================================================
# 服务端口配置
# =============================================================================
# Mem0 API 服务端口（默认：8000）
API_PORT=8000

# Qdrant 向量数据库端口
QDRANT_PORT=6333
QDRANT_GRPC_PORT=6334

# Neo4j 端口
NEO4J_HTTP_PORT=7474
NEO4J_BOLT_PORT=7687

# =============================================================================
# PostgreSQL 配置（可选 - Qdrant 的替代方案）
# =============================================================================
# 使用 pgvector 的 PostgreSQL 向量存储（Qdrant 的替代方案）
POSTGRES_HOST=postgres
POSTGRES_PORT=5432
POSTGRES_DB=mem0
POSTGRES_USER=postgres
POSTGRES_PASSWORD=postgres
POSTGRES_COLLECTION_NAME=mem0_vectors

# =============================================================================
# 第三方 API 配置示例
# =============================================================================
# 流行的第三方 API 提供商配置示例

# DeepSeek API 配置
# OPENAI_API_KEY=sk-your-deepseek-api-key
# OPENAI_BASE_URL=https://api.deepseek.com/v1
# OPENAI_MODEL=deepseek-chat

# 智谱 AI 配置
# OPENAI_API_KEY=your-zhipu-api-key
# OPENAI_BASE_URL=https://open.bigmodel.cn/api/paas/v4
# OPENAI_MODEL=glm-4

# OpenRouter 配置
# OPENROUTER_API_KEY=your-openrouter-api-key
# OPENAI_MODEL=openai/gpt-4

# =============================================================================
# 生产环境部署设置
# =============================================================================
# 生产环境部署时，请考虑以下设置：

# 数据持久化（生产环境路径）
# MEM0_DATA_PATH=/var/lib/mem0/data
# NEO4J_DATA_PATH=/var/lib/mem0/neo4j/data
# QDRANT_DATA_PATH=/var/lib/mem0/qdrant

# 安全设置
# NEO4J_PASSWORD=your-secure-password-here
# POSTGRES_PASSWORD=your-secure-password-here

# 性能调优
# NEO4J_dbms_memory_heap_max_size=4G
# NEO4J_dbms_memory_pagecache_size=2G
